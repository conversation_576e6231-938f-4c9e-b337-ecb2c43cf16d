from __future__ import annotations

from abc import abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Type

import rlp
from serde import field, from_dict, serde, to_dict  # is pyserde not serde

from common.atomic import Atomic
from common.encode import Model, RLPModel, bytesToHex, encodeRLP, hexToBytes, toJson
from common.utils import doubleSha256Hex

from .bloom import BloomFilter
from .transaction import Transaction, TransactionRLP


@serde
@dataclass
class AbstractHeader(Model):
    """
    Abstract class for block
    """

    parent_hash: bytes = field(
        serializer=bytesToHex,
        deserializer=hexToBytes,
    )
    height: int = field()
    state_root: str = field()
    transactions_root: str = field()
    receipts_root: str = field()
    bloom: BloomFilter = field(
        default_factory=BloomFilter, serializer=BloomFilter._toHex, deserializer=BloomFilter.fromHex
    )

    def toEnvironment(self) -> Dict[str, str]:
        """
        Return block environment for wasm runtime
        """
        return {"block_height": str(self.height)}

    def hash(self) -> bytes:
        """
        Return the block header hash
        double sha256 hash of the block header
        """
        return hexToBytes(doubleSha256Hex(encodeRLP(self)))

    def __hash__(self) -> int:
        """
        Return hash of the block header

        implement __hash__ for storing block in set
        """
        return hash(self.hash())

    def hashHex(self) -> str:
        """
        Return hash hex of the block header
        """
        return bytesToHex(self.hash())

    @abstractmethod
    def toSignableMessage(self) -> str:
        """
        Return block data to sign
        """
        raise NotImplementedError


@serde
@dataclass(init=False)
class SPOSHeader(AbstractHeader):
    local_timestamp: int = field()
    protocol_timestamp: int = field()
    slot_id: int = field()
    proposer_address: bytes = field(serializer=bytesToHex, deserializer=hexToBytes)

    # public keys of validators
    public_keys: List[str] = field(default_factory=list)

    # signatures of validators
    signatures: List[str] = field(default_factory=list)

    def __init__(
        self,
        parent_hash: bytes,
        height: int,
        state_root: str,
        transactions_root: str,
        receipts_root: str,
        bloom: BloomFilter,
        local_timestamp: int,
        protocol_timestamp: int,
        slot_id: int,
        proposer_address: bytes,
        public_keys: List[str],
        signatures: List[str],
    ):
        super().__init__(
            parent_hash=parent_hash,
            height=height,
            state_root=state_root,
            transactions_root=transactions_root,
            receipts_root=receipts_root,
            bloom=bloom,
        )
        self.local_timestamp = local_timestamp
        self.protocol_timestamp = protocol_timestamp
        self.slot_id = slot_id
        self.proposer_address = proposer_address
        self.public_keys = public_keys
        self.signatures = signatures

    def toSignableMessage(self) -> str:
        """
        Return block data to sign
        """
        skipKeys = {"public_keys", "signatures", "state_root", "receipts_root"}
        blockDict = {k: v for k, v in to_dict(self).items() if k not in skipKeys}
        return toJson(blockDict)

    def toRLP(self) -> RLPModel:
        return SPOSHeaderRLP(
            self.parent_hash,
            self.height,
            hexToBytes(self.state_root),
            hexToBytes(self.transactions_root),
            hexToBytes(self.receipts_root),
            self.bloom.toBytes(),
            self.local_timestamp,
            self.protocol_timestamp,
            self.slot_id,
            self.proposer_address,
            [hexToBytes(pk) for pk in self.public_keys],
            [hexToBytes(sig) for sig in self.signatures],
        )

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return SPOSHeaderRLP


class SPOSHeaderRLP(RLPModel):
    fields = [
        ("parent_hash", rlp.sedes.binary),
        ("height", rlp.sedes.big_endian_int),
        ("state_root", rlp.sedes.binary),
        ("transactions_root", rlp.sedes.binary),
        ("receipts_root", rlp.sedes.binary),
        ("bloom", rlp.sedes.binary),
        ("local_timestamp", rlp.sedes.big_endian_int),
        ("protocol_timestamp", rlp.sedes.big_endian_int),
        ("slot_id", rlp.sedes.big_endian_int),
        ("proposer_address", rlp.sedes.binary),
        ("public_keys", rlp.sedes.CountableList(rlp.sedes.binary)),
        ("signatures", rlp.sedes.CountableList(rlp.sedes.binary)),
    ]

    def toModel(self) -> Model:
        return SPOSHeader(
            self.parent_hash,
            self.height,
            bytesToHex(self.state_root),
            bytesToHex(self.transactions_root),
            bytesToHex(self.receipts_root),
            BloomFilter.fromBytes(self.bloom),
            self.local_timestamp,
            self.protocol_timestamp,
            self.slot_id,
            self.proposer_address,
            [bytesToHex(pk) for pk in self.public_keys],
            [bytesToHex(sig) for sig in self.signatures],
        )


@serde
@dataclass(init=False)
class POWHeader(AbstractHeader):
    difficulty_score: int = field()
    difficulty_score_overall: int = field()
    timestamp: int = field()
    nonce: int = field()
    multiplier: int = field()

    def __init__(
        self,
        parent_hash: bytes,
        height: int,
        state_root: str,
        transactions_root: str,
        receipts_root: str,
        bloom: BloomFilter,
        difficulty_score: int,
        difficulty_score_overall: int,
        timestamp: int,
        nonce: int,
        multiplier: int,
    ):
        super().__init__(
            parent_hash=parent_hash,
            height=height,
            state_root=state_root,
            transactions_root=transactions_root,
            receipts_root=receipts_root,
            bloom=bloom,
        )
        self.difficulty_score = difficulty_score
        self.difficulty_score_overall = difficulty_score_overall
        self.timestamp = timestamp
        self.nonce = nonce
        self.multiplier = multiplier

    def toSignableMessage(self) -> str:
        """
        Return block data to sign
        """
        raise NotImplementedError("Only spos block can be signed")

    def toRLP(self) -> RLPModel:
        return POWHeaderRLP(
            self.parent_hash,
            self.height,
            hexToBytes(self.state_root),
            hexToBytes(self.transactions_root),
            hexToBytes(self.receipts_root),
            self.bloom.toBytes(),
            self.difficulty_score,
            self.difficulty_score_overall,
            self.timestamp,
            self.nonce,
            self.multiplier,
        )

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return POWHeaderRLP


class POWHeaderRLP(RLPModel):
    fields = [
        ("parent_hash", rlp.sedes.binary),
        ("height", rlp.sedes.big_endian_int),
        ("state_root", rlp.sedes.binary),
        ("transactions_root", rlp.sedes.binary),
        ("receipts_root", rlp.sedes.binary),
        ("bloom", rlp.sedes.binary),
        ("difficulty_score", rlp.sedes.big_endian_int),
        ("difficulty_score_overall", rlp.sedes.big_endian_int),
        ("timestamp", rlp.sedes.big_endian_int),
        ("nonce", rlp.sedes.big_endian_int),
        ("multiplier", rlp.sedes.big_endian_int),
    ]

    def toModel(self) -> Model:
        return POWHeader(
            self.parent_hash,
            self.height,
            bytesToHex(self.state_root),
            bytesToHex(self.transactions_root),
            bytesToHex(self.receipts_root),
            BloomFilter.fromBytes(self.bloom),
            self.difficulty_score,
            self.difficulty_score_overall,
            self.timestamp,
            self.nonce,
            self.multiplier,
        )


@serde
@dataclass
class Body(Model):
    transactions: List[Transaction]

    def toRLP(self) -> RLPModel:
        rlpTransactions = [transaction.toRLP() for transaction in self.transactions]
        return BodyRLP(rlpTransactions)

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return BodyRLP


class BodyRLP(RLPModel):
    fields = [
        ("transactions", rlp.sedes.CountableList(TransactionRLP)),
    ]

    def toModel(self) -> Model:
        return Body([transaction.toModel() for transaction in self.transactions])


def headerDeserializer(data):
    if "protocol_timestamp" in data:
        return from_dict(SPOSHeader, data)
    else:
        return from_dict(POWHeader, data)


@serde
@dataclass
class Block:
    header: AbstractHeader = field(deserializer=headerDeserializer)
    transactions: List[Transaction] = field(default_factory=list)

    # cache the block hash
    hashCache: Atomic[Optional[bytes]] = field(
        default_factory=lambda: Atomic(None), skip=True, compare=False, init=False
    )

    def hash(self) -> bytes:
        """
        Return the block hash, from cache or calculate it
        """
        # if state root is empty, the block hash is not finalized
        if (blockHash := self.hashCache.get()) is not None:
            return blockHash
        blockHash = self.header.hash()
        self.hashCache.set(blockHash)
        return blockHash

    def resetHashCache(self):
        """
        Reset the block hash cache
        """
        self.hashCache.set(None)

    def hashHex(self) -> str:
        """
        Return hash hex of the block header
        """
        return self.header.hashHex()

    def height(self) -> int:
        """
        Return the block height
        """
        return self.header.height

    def root(self) -> str:
        """
        Return the block state root
        """
        return self.header.state_root

    def parentHash(self) -> bytes:
        """
        Return the block parent hash
        """
        return self.header.parent_hash

    def transactionsRoot(self) -> str:
        """
        Return the block transactions root
        """
        return self.header.transactions_root

    def receiptsRoot(self) -> str:
        """
        Return the block receipts root
        """
        return self.header.receipts_root

    def toEnvironment(self) -> Dict[str, str]:
        """
        Return block environment for wasm runtime
        """
        return self.header.toEnvironment()

    def toSignableMessage(self) -> str:
        """
        Return block data to sign
        """
        return self.header.toSignableMessage()

    def __hash__(self):
        """
        Return hash of the block header

        implement __hash__ for storing block in set
        """
        return hash(self.hash())

    def __eq__(self, other):
        """
        Compare two blocks
        """
        return self.hash() == other.hash()
