from eth_utils import keccak

from common.encode import bytesToHex
from common.models import (
    OperationCallContract,
    OperationCreateContract,
    OperationForkContract,
    OperationType,
    OperationUpgradeContract,
    Transaction,
)
from common.signature import signTransactionWithPrivateKey

PRIVATE_KEY1 = "0x65f6fc2bb810b011ce076b52263a1a0e1c809243b1c252b3a7c8c64371c433015a3d65f60833cddcc8bf3a4951a609f806c03a7090f47b6822caef965e446ff9"
PUBLIC_KEY1 = "0x82cc75fe87bfc8b67cb3544460a7cce8cf86b5cddc7205550e5d011299923f2c"
ADDRESS1 = "0xb34421d7ae0feb3dcf10ac9b6ae4e12a3253d61a004791517f"

PRIVATE_KEY2 = "0x01c16a3ddf49312573e04b954ad48fc6db8efdc5fa49af216f731d4aa366b000187f4358665a0807044e1aeed941abc3f5d15d6f295b4257db567076aaa3b5e3"
PUBLIC_KEY2 = "0x9a81270552d30238f0a8fc7ce7b5c114685d25dcf795b3072e63c541d565bc42"
ADDRESS2 = "0xbd059c3365b6a51553144a57cf01686f9c9cf50200a7a3be1f"

PRIVATE_KEY3 = "0x75e22fe2074735d57b23bb161ad4f80a8ff0f7ec28f4f54474a69330112a6c031e672b650523ffc82d8dfe7388df738f663f6ce6294417947d8dda8e62ae7037"
PUBLIC_KEY3 = "0xeeefb6b1eba9b8d76025dc3915c6172e29a35d41d05c6dcd61d35a0c8de50a61"
ADDRESS3 = "0xc76961079146c12830a3b55dd44cb5e5b4418577002f99fd05"

DEFAULT_TOKEN = "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3"
PANIC_TOKEN = "0xba88c1408cb09a0e3952b3097f01bc4abba0a19e5aa2f0a53ae97ed25391ff8a"

# -------- Define the transactions for testing --------

# create contract transaction
with open("contract/base/token.wasm", "rb") as f:
    CONTRACT_CODE = f.read()

CONTRACT_CODE_HEX = bytesToHex(CONTRACT_CODE)
CONTRACT_CODE_HASH = keccak(CONTRACT_CODE).hex()

CREATE_TOKEN_CONTRACT_TX = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCreateContract(
            op_type=OperationType.CREATE_CONTRACT,
            contract_hex_bytecode=CONTRACT_CODE_HEX,
            constructor_parameters=["pytest_token"],
            contract_source_url="xxx",
            upgradable=False,
            git_commit_hash="1234567890abcdef",
            reproducible_build=True,
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=1000000,
    ),
    PRIVATE_KEY1,
)

# issue transaction
TOKEN_ISSUE_TX = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
            function_name="issue",
            parameters=[ADDRESS1, 1000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=1000000,
    ),
    PRIVATE_KEY1,
)

# transfer transaction
TOKEN_TRANSFER_TX = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
            function_name="transfer",
            parameters=[ADDRESS1, ADDRESS2, 100],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=1000000,
    ),
    PRIVATE_KEY1,
)

TOKEN_ISSUE_TX_SUFFICIENT_FUEL = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address=DEFAULT_TOKEN,
            function_name="issue",
            parameters=[ADDRESS1, 100_0000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=10_0000,
    ),
    PRIVATE_KEY1,
)

# transfer transaction without fuel
TOKEN_TRANSFER_TX_SUFFICIENT_FUEL = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address=DEFAULT_TOKEN,
            function_name="transfer",
            parameters=[ADDRESS1, ADDRESS2, 20_0000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=20_0000,
    ),
    PRIVATE_KEY1,
)

# transfer transaction without enough fuel
TOKEN_TRANSFER_TX_INSUFFICIENT_FUEL = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address=DEFAULT_TOKEN,
            function_name="transfer",
            parameters=[ADDRESS1, ADDRESS2, 10000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=10000,
    ),
    PRIVATE_KEY1,
)

# transfer transaction with sufficient fuel
TOKEN_PANIC_TRANSFER_TX_SUFFICIENT_FUEL = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address=DEFAULT_TOKEN,
            function_name="panic_transfer",
            parameters=[ADDRESS1, ADDRESS2, 10000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=10_0000,
    ),
    PRIVATE_KEY1,
)


PANIC_TOKEN_ISSUE_TX = Transaction(
    dependent_transaction_hash="",
    sender=ADDRESS1,
    op_data=OperationCallContract(
        op_type=OperationType.CALL_CONTRACT,
        contract_address=PANIC_TOKEN,
        function_name="issue",
        parameters=[ADDRESS1, 100_0000],
    ),
    public_keys=[],
    signatures=[],
    timestamp=0,
    fuel=10_0000,
)

# panic token contract: transfer function has a panic
PANIC_TOKEN_TRANSFER_TX = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address=PANIC_TOKEN,
            function_name="transfer",
            parameters=[ADDRESS1, ADDRESS2, 10000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=20_0000,
    ),
    PRIVATE_KEY1,
)

# fork contract transaction
FORK_TOKEN_CONTRACT_TX = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationForkContract(
            op_type=OperationType.FORK_CONTRACT,
            contract_code_hash=CONTRACT_CODE_HASH,
            constructor_parameters=["pytest_token1"],
            contract_source_url="xxx",
            upgradable=False,
            git_commit_hash="abc123",
            reproducible_build=True,
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=10_0000,
    ),
    PRIVATE_KEY1,
)

# upgrade contract transaction
with open("vm/tests/token_lmdb_demo.wasm", "rb") as f:
    UPGRADED_CODE = f.read()

UPGRADED_CODE_HEX = bytesToHex(UPGRADED_CODE)
UPGRADED_CODE_HASH = keccak(UPGRADED_CODE).hex()

UPGRADE_TOKEN_CONTRACT_TX = signTransactionWithPrivateKey(
    Transaction(
        dependent_transaction_hash="",
        sender=ADDRESS1,
        op_data=OperationUpgradeContract(
            op_type=OperationType.UPGRADE_CONTRACT,
            contract_address="",  # will be attached in the test
            contract_hex_bytecode=bytesToHex(UPGRADED_CODE),
            contract_source_url="xxx",
            git_commit_hash="abc123",
            reproducible_build=True,
        ),
        public_keys=[],
        signatures=[],
        timestamp=0,
        fuel=1000000,
    ),
    PRIVATE_KEY1,
)

# 200 transactions, 100 issue and 100 transfer
TRANSACTIONS_200 = []
for _ in range(100):
    TRANSACTIONS_200.append(TOKEN_ISSUE_TX)
    TRANSACTIONS_200.append(TOKEN_TRANSFER_TX)
