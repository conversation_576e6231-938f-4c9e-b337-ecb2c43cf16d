import time
from typing import Any, Dict, List, Optional, Tuple

from kivy.logger import Logger

import rawdb
from common import (
    AccountAttributes,
    ContractExecutionError,
    UnsupportedOperationTypeError,
    bytesToHex,
    generateContractAddress,
    hexToBytes,
)
from common.models import (
    OperationCallContract,
    OperationCallContractResult,
    OperationCreateContract,
    OperationCreateContractResult,
    OperationForkContract,
    OperationForkContractResult,
    OperationResult,
    OperationUpgradeContract,
    OperationUpgradeContractResult,
    Receipt,
    Transaction,
    TransactionExecutionStatus,
)
from config import CONFIG
from state import StateDB
from vm import ContractExecutor, mergeEnv


class TransactionProcessor:
    """
    The TransactionProcessor class is responsible for handling and applying various types of transactions
    within a blockchain environment.
    It interacts with the state database manager to manage the state of the blockchain, apply transactions, and handle contract operations such as creating, calling, and forking
    contracts.
    The class ensures that transactions are applied correctly, manages snapshots for state consistency, and generates receipts for each transaction processed.
    """

    _LOGGER_TITLE = "TransactionProcessor: "

    def applyTransaction(
        self,
        state: StateDB,
        transaction: Transaction,
        blockEnv: Dict,
        transactionIndex: int,
        useFuel: bool = True,
        allowError: bool = True,
    ) -> Receipt:
        """
        Apply a transaction.

        Remember to catch exception when calling this function.
        Manage The snapshot in this function.
        """
        # Consume fuel for the transaction execution
        # consume fuel before snapshot, always consume even if transaction execution failed.
        consumeFuelStatus, consumeFuelResult = self.consume_fuel(state, transaction, useFuel)

        snapshot = state.snapshot()
        # if consume fuel failed, return receipt with status failure and opResult.
        if consumeFuelStatus == TransactionExecutionStatus.FAILURE and consumeFuelResult is not None:
            # TODO: record post state in receipt
            state.intermediateRoot()
            return Receipt(
                transaction_hash=transaction.hash(),
                transaction_index=transactionIndex,
                status=consumeFuelStatus,
                op_result=consumeFuelResult,
                block_hash=b"",  # empty for now, will be filled after state is committed
            )

        # set the transasction context
        transactionHash = transaction.hash()
        transactionEnv = transaction.toEnvironment(transactionIndex)
        finalEnv = mergeEnv(blockEnv, transactionEnv)
        fuel = transaction.fuel
        Logger.debug(f"{self._LOGGER_TITLE} Applying transaction {bytesToHex(transactionHash)}")
        state.setTxContext(transactionHash, transactionIndex)

        if isinstance(transaction.op_data, OperationCreateContract):
            status, opResult = self._applyCreateContractOperation(
                state, fuel, transaction.op_data, transaction.sender, transaction.timestamp, finalEnv
            )
        elif isinstance(transaction.op_data, OperationCallContract):
            status, opResult = self._applyCallContractOperation(state, fuel, transaction.op_data, finalEnv, allowError)
        elif isinstance(transaction.op_data, OperationForkContract):
            status, opResult = self._applyForkContractOperation(
                state, fuel, transaction.op_data, transaction.sender, transaction.timestamp, finalEnv
            )
        elif isinstance(transaction.op_data, OperationUpgradeContract):
            status, opResult = self._applyUpgradeContractOperation(state, transaction.op_data, transaction.sender)
        else:
            raise UnsupportedOperationTypeError("Unsupported operation type")

        if status == TransactionExecutionStatus.FAILURE:
            state.revertToSnapshot(snapshot)

        # get logs after transaction execution
        logs = state.getLogs(txHash=transactionHash)

        # TODO: record post state in receipt
        state.intermediateRoot()

        return Receipt(
            transaction_hash=transactionHash,
            transaction_index=transactionIndex,
            status=status,
            op_result=opResult,
            block_hash=b"",  # empty for now, will be filled after state is committed
            logs=logs,
        )

    def consume_fuel(
        self, state: StateDB, transaction: Transaction, useFuel: bool
    ) -> Tuple[TransactionExecutionStatus, Optional[OperationResult]]:
        """
        Consume fuel token for transaction execution.
        """
        fuel = transaction.fuel

        if not useFuel:
            return TransactionExecutionStatus.SUCCESS, None
        tokenContractAddress = CONFIG.tokenAddress
        burn_address = "0x0000000000000000000000000000000000000000000000000000000000000000"
        # use token contract executor to consume token.
        contractExecutor = ContractExecutor(address=tokenContractAddress)
        # set fuel as 10000000 to consume fuel token.
        fuelToExecute = 10000000
        # take snapshot before consume fuel with transfer operation.
        snapshot = state.snapshot()

        # set sender to envs to pass it to the contract due to caller validation.
        envs = {
            "sender": transaction.sender,
        }
        transferResult = contractExecutor.executeWithEnv(
            state, fuelToExecute, envs, "transfer", None, transaction.sender, burn_address, fuel
        )
        result, err = None, None
        if isinstance(transferResult, tuple):
            result, err = transferResult
        if err is not None:
            Logger.warning(f"{self._LOGGER_TITLE} Failed to consume fuel from {transaction.sender} due to error: {err}")
            # handle error operation result for different operation type.
            if isinstance(transaction.op_data, OperationCreateContract):
                opResult = OperationCreateContractResult(
                    op_type=transaction.op_data.op_type, contract_address=err, code_hash=""
                )
            elif isinstance(transaction.op_data, OperationCallContract):
                opResult = OperationCallContractResult(op_type=transaction.op_data.op_type, return_data=err)
            else:
                opResult = OperationForkContractResult(
                    op_type=transaction.op_data.op_type, contract_address=err, code_hash=""
                )
            # if transfer failed, revert state to snapshot.
            state.revertToSnapshot(snapshot)
            return TransactionExecutionStatus.FAILURE, opResult
        # if consume fuel successfully, this operation result will not be recorded in receipt.
        return TransactionExecutionStatus.SUCCESS, None

    def applyTransactions(
        self, state: StateDB, blockEnv: Dict, transactions: List[Transaction], receiptList: List[Receipt] = None
    ) -> List[Receipt]:
        """
        Apply transactions in the block
        """
        startTime = time.time()
        # super node don't have token at initial stage (block_height: 1)
        blockHeight = int(blockEnv.get("block_height", 0))
        useFuel = blockHeight > 1
        if receiptList is None:
            receiptList = []
        for i, transaction in enumerate(transactions):
            # Why it's `i + 1` here:
            # the first transaction is minting transaction, which is not included in the transactions
            transactionIndex = i + 1
            try:
                receipt = self.applyTransaction(state, transaction, blockEnv, transactionIndex, useFuel)
                receiptList.append(receipt)
            except Exception as e:
                Logger.error(f"{self._LOGGER_TITLE} Apply transaction {bytesToHex(transaction.hash())} failed: {e}")

        stopTime = time.time()
        Logger.debug(f"{self._LOGGER_TITLE} Applied {len(transactions)} transactions in {stopTime - startTime}s")
        return receiptList

    def _applyCreateContractOperation(
        self,
        state: StateDB,
        fuel: int,
        operation: OperationCreateContract,
        senderAddress: str,
        timestamp: int,
        env: Dict = None,
    ) -> Tuple[TransactionExecutionStatus, OperationCreateContractResult]:
        """
        Apply operation create contract
        """
        # 1. parse transaction data
        try:
            contractByteCode = hexToBytes(operation.contract_hex_bytecode)
            contractAddressBytes = generateContractAddress(operation.contract_hex_bytecode, senderAddress, timestamp)
            contractAddressHex = bytesToHex(contractAddressBytes)
        except Exception as e:
            Logger.warning(f"{self._LOGGER_TITLE} Failed to parse hex code: {e}")
            err = "invalid hex contract bytecode"
            return TransactionExecutionStatus.FAILURE, OperationCreateContractResult(
                op_type=operation.op_type, contract_address=err, code_hash=""
            )

        if state.hasAccount(contractAddressBytes):
            err = f"Contract {contractAddressHex} already exists"
            Logger.warning(f"{self._LOGGER_TITLE} {err}")
            return TransactionExecutionStatus.FAILURE, OperationCreateContractResult(
                op_type=operation.op_type,
                contract_address=err,
                code_hash="",
            )

        state.createContract(contractAddressBytes)
        state.setCode(contractAddressBytes, contractByteCode)
        attrs = AccountAttributes(
            deployer=hexToBytes(senderAddress),
            upgradable=operation.upgradable,
            contractSourceUrl=operation.contract_source_url,
            gitCommitHash=operation.git_commit_hash,
            reproducibleBuild=operation.reproducible_build,
        )
        state.setContractAttributes(
            address=contractAddressBytes,
            attrs=attrs,
        )
        codeHash = bytesToHex(state.getCodeHash(contractAddressBytes))

        executor = ContractExecutor(address=contractAddressBytes)
        _, err = executor.constructorWithEnv(state, fuel, env, *operation.constructor_parameters)
        if err is not None:
            Logger.warning(f"{self._LOGGER_TITLE} Failed to execute constructor: {err}.\n Revert to snapshot...")

            return TransactionExecutionStatus.FAILURE, OperationCreateContractResult(
                op_type=operation.op_type, contract_address=err, code_hash=""
            )

        # update code usage count
        if operation.upgradable:
            rawdb.addCodeUsageCount(state.db.diskDB(), state.getCodeHash(contractAddressBytes))

        Logger.debug(f"{self._LOGGER_TITLE} Create contract {contractAddressHex} successfully")
        return TransactionExecutionStatus.SUCCESS, OperationCreateContractResult(
            op_type=operation.op_type, contract_address=contractAddressHex, code_hash=codeHash
        )

    def _applyCallContractOperation(
        self, state: StateDB, fuel: int, operation: OperationCallContract, env: Dict, allowError: bool = True
    ) -> Tuple[TransactionExecutionStatus, OperationCallContractResult]:
        """
        Apply operation call contract
        """
        contractAddress = operation.contract_address
        functionName = operation.function_name
        args = operation.parameters

        contractExecutor = ContractExecutor(address=contractAddress)

        resultTuple = contractExecutor.executeWithEnv(state, fuel, env, functionName, Any, *args)
        result, err = None, None
        if isinstance(resultTuple, tuple):
            result, err = resultTuple
        else:
            err = resultTuple

        if err is not None:
            Logger.warning(
                f"{self._LOGGER_TITLE} Failed to execute contract {contractAddress} function {functionName} due to error: {err}"
            )

            if not allowError:
                raise ContractExecutionError(
                    f"Failed to execute contract {contractAddress} function {functionName} due to error: {err}"
                )

            return TransactionExecutionStatus.FAILURE, OperationCallContractResult(
                op_type=operation.op_type, return_data=err
            )

        Logger.debug(
            f"{self._LOGGER_TITLE} Run contract {contractAddress} function: {functionName} with args: {args}. Apply transaction successfully."
        )
        return TransactionExecutionStatus.SUCCESS, OperationCallContractResult(
            op_type=operation.op_type, return_data=result
        )

    def _applyForkContractOperation(
        self,
        state: StateDB,
        fuel: int,
        operation: OperationForkContract,
        senderAddress: str,
        timestamp: int,
        env: Dict = None,
    ) -> Tuple[TransactionExecutionStatus, OperationForkContractResult]:
        """
        Apply operation fork contract
        """

        try:
            targetContractCodeHash = hexToBytes(operation.contract_code_hash)
            code = state.getCodeByHash(targetContractCodeHash)
        except Exception as e:
            Logger.warning(f"{self._LOGGER_TITLE} Failed to retrieve code of the forked contract: {e}")
            err = "invalid contract code hash"
            return TransactionExecutionStatus.FAILURE, OperationCreateContractResult(
                op_type=operation.op_type, contract_address=err, code_hash=""
            )

        contractAddressBytes = generateContractAddress(bytesToHex(code), senderAddress, timestamp)
        contractAddressHex = bytesToHex(contractAddressBytes)

        state.createContract(contractAddressBytes)
        state.setCode(contractAddressBytes, code)
        attrs = AccountAttributes(
            deployer=hexToBytes(senderAddress),
            upgradable=operation.upgradable,
            contractSourceUrl=operation.contract_source_url,
            gitCommitHash=operation.git_commit_hash,
            reproducibleBuild=operation.reproducible_build,
        )
        state.setContractAttributes(
            address=contractAddressBytes,
            attrs=attrs,
        )

        executor = ContractExecutor(address=contractAddressBytes)
        _, err = executor.constructorWithEnv(state, fuel, env, *operation.constructor_parameters)
        if err is not None:
            Logger.warning(f"{self._LOGGER_TITLE} Failed to execute constructor: {err}.\n Revert to snapshot...")
            return TransactionExecutionStatus.FAILURE, OperationCreateContractResult(
                op_type=operation.op_type, contract_address=err, code_hash=""
            )

        # update code usage count
        if operation.upgradable:
            rawdb.addCodeUsageCount(state.db.diskDB(), state.getCodeHash(contractAddressBytes))

        Logger.debug(f"{self._LOGGER_TITLE} Fork contract {contractAddressHex} successfully")
        return TransactionExecutionStatus.SUCCESS, OperationForkContractResult(
            op_type=operation.op_type,
            contract_address=contractAddressHex,
            code_hash=bytesToHex(state.getCodeHash(contractAddressBytes)),
        )

    def _applyUpgradeContractOperation(
        self, state: StateDB, operation: OperationUpgradeContract, senderAddress: str
    ) -> Tuple[TransactionExecutionStatus, OperationUpgradeContractResult]:
        """
        Apply operation upgrade contract
        """
        # parse transaction data
        try:
            contractByteCode = hexToBytes(operation.contract_hex_bytecode)
            contractAddressBytes = hexToBytes(operation.contract_address)
        except Exception as e:
            Logger.warning(f"{self._LOGGER_TITLE} Failed to parse hex: {e}")
            err = "invalid hex contract bytecode or contract address"
            return TransactionExecutionStatus.FAILURE, OperationUpgradeContractResult(
                op_type=operation.op_type, code_hash=err
            )

        # check if contract exists
        account = state.getAccount(contractAddressBytes)
        if not account:
            err = f"Contract {operation.contract_address} does not exist"
            Logger.warning(f"{self._LOGGER_TITLE} {err}")
            return TransactionExecutionStatus.FAILURE, OperationUpgradeContractResult(
                op_type=operation.op_type, code_hash=err
            )

        # check if contract is upgradable
        if not account.attrs.upgradable:
            err = f"Contract {operation.contract_address} is not upgradable"
            Logger.warning(f"{self._LOGGER_TITLE} {err}")
            return TransactionExecutionStatus.FAILURE, OperationUpgradeContractResult(
                op_type=operation.op_type, code_hash=err
            )

        # check if sender is the deployer
        # TODO: should be an access list
        if account.attrs.deployer != hexToBytes(senderAddress):
            err = f"Sender {senderAddress} is not the deployer of contract {operation.contract_address}"
            Logger.warning(f"{self._LOGGER_TITLE} {err}")
            return TransactionExecutionStatus.FAILURE, OperationUpgradeContractResult(
                op_type=operation.op_type, code_hash=err
            )

        # upgrade contract
        oldCodeHash = state.getCodeHash(contractAddressBytes)
        state.setCode(contractAddressBytes, contractByteCode)
        attrs = AccountAttributes(
            deployer=hexToBytes(senderAddress),
            upgradable=True,
            contractSourceUrl=operation.contract_source_url,
            gitCommitHash=operation.git_commit_hash,
            reproducibleBuild=operation.reproducible_build,
        )
        state.setContractAttributes(
            address=contractAddressBytes,
            attrs=attrs,
        )

        # clean-up vm cache
        if ContractExecutor.moduleCache.get(contractAddressBytes, None) is not None:
            del ContractExecutor.moduleCache[contractAddressBytes]

        # update code usage count
        newCodeHash = state.getCodeHash(contractAddressBytes)
        rawdb.addCodeUsageCount(state.db.diskDB(), newCodeHash)
        rawdb.subCodeUsageCount(state.db.diskDB(), oldCodeHash)

        codeHash = bytesToHex(newCodeHash)
        Logger.debug(f"{self._LOGGER_TITLE} Upgrade contract {operation.contract_address} successfully")
        return TransactionExecutionStatus.SUCCESS, OperationUpgradeContractResult(
            op_type=operation.op_type, code_hash=codeHash
        )
