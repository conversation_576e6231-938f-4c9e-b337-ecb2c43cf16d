#[glue::contract]
mod token {
    #[glue::storage]
    pub struct Token {
        pub token_name: glue::StorageField<String>,
        pub total_supply: glue::StorageField<i64>,
        pub balances: glue::collections::Map<String, i64>, // address -> balance
    }

    impl Token {
        #[glue::constructor]
        pub fn new(token_name: String) -> Self {
            println!("new token: {:?}", token_name);
            Self {
                token_name: glue::StorageField::new(&token_name),
                total_supply: glue::StorageField::new(&0),
                balances: glue::collections::Map::new(),
            }
        }


        #[glue::atomic]
        pub fn issue(&mut self, address: String, amount: i64) -> anyhow::Result<()> {
            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }
            let balance = self.balances.get(&address).unwrap_or(0);
            self.balances.insert(&address, &(balance + amount));
            self.total_supply.set(&(self.total_supply.get() + amount));
            Ok(())
        }


        #[glue::readonly]
        pub fn balance(&self, address: String) -> i64 {
            self.balances.get(&address).unwrap_or(0)
        }


        #[glue::atomic]
        pub fn transfer(&mut self, from: String, to: String, amount: i64) -> anyhow::Result<()> {
            let env = env();
            let callers = env.callers;
            if !callers.contains(&from) {
                return Err(anyhow::anyhow!("from address is not in the callers"));
            }

            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }

            let from_balance = self.balance(from.clone());
            if from_balance < amount {
                return Err(anyhow::anyhow!("insufficient from account balance"));
            }
            self.balances.insert(&from, &(from_balance - amount));

            let to_balance = self.balance(to.clone());
            self.balances.insert(&to, &(to_balance + amount));

            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[glue::test]
    fn test_issue_transfer_success() {
        token::set_instance(token::Token::new("test".to_string()));
        let token = token::get_instance();
        token.issue("alice".to_string(), 100).unwrap();
        let balance = token.balance("alice".to_string());
        assert_eq!(balance, 100);
        let balance = token.balance("bob".to_string());
        assert_eq!(balance, 0);

        // transfoer without callers env
        let result = token.transfer("alice".to_string(), "bob".to_string(), 10);
        assert!(result.is_err());

        let env2 = glue::env::TestEnvironment {
            block_height: 1,
            transaction_hash: "transaction_hash_1".to_string(),
            transaction_index: 2,
            sender: "alice".to_string(),
            callers: vec!["alice".to_string()],
            transaction_timestamp: 3,
        };
        glue::env::update_test_env(&env2);
        token.transfer("alice".to_string(), "bob".to_string(), 10).unwrap();
        let balance = token.balance("alice".to_string());
        assert_eq!(balance, 90);
        let balance = token.balance("bob".to_string());
        assert_eq!(balance, 10);
    }
}
