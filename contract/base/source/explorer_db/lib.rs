#[glue::contract]
mod explorer_db {
    use serde::{Deserialize, Serialize};

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "u8", from = "u8")]
    pub enum OperationType {
        CreateContract,
        CallContract,
        ForkContract,
        UpgradeContract,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "bool", from = "bool")]
    pub enum TransactionExecutionStatus {
        Failure,
        Success,
    }

    impl From<TransactionExecutionStatus> for bool {
        fn from(status: TransactionExecutionStatus) -> bool {
            match status {
                TransactionExecutionStatus::Failure => false,
                TransactionExecutionStatus::Success => true,
            }
        }
    }

    impl From<bool> for TransactionExecutionStatus {
        fn from(value: bool) -> Self {
            match value {
                false => TransactionExecutionStatus::Failure,
                true => TransactionExecutionStatus::Success,
            }
        }
    }

    impl From<OperationType> for u8 {
        fn from(op_type: OperationType) -> u8 {
            match op_type {
                OperationType::CreateContract => 0,
                OperationType::CallContract => 1,
                OperationType::ForkContract => 2,
                OperationType::UpgradeContract => 3,
            }
        }
    }

    impl From<u8> for OperationType {
        fn from(value: u8) -> Self {
            match value {
                0 => OperationType::CreateContract,
                1 => OperationType::CallContract,
                2 => OperationType::ForkContract,
                3 => OperationType::UpgradeContract,
                _ => panic!("Invalid operation type value"),
            }
        }
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct CreateContractOperation {
        pub op_type: OperationType,
        pub contract_hex_bytecode: String,
        pub constructor_parameters: Vec<serde_json::Value>,
        pub contract_source_url: String,
        pub upgradable: bool,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct CallContractOperation {
        pub op_type: OperationType,
        pub contract_address: String,
        pub function_name: String,
        pub parameters: Vec<serde_json::Value>,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct ForkContractOperation {
        pub op_type: OperationType,
        pub contract_code_hash: String,
        pub constructor_parameters: Vec<serde_json::Value>,
        pub contract_source_url: String,
        pub upgradable: bool,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct UpgradeContractOperation {
        pub op_type: OperationType,
        pub contract_address: String,
        pub contract_hex_bytecode: String,
        pub contract_source_url: String,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(untagged)]
    pub enum OperationData {
        CreateContract(CreateContractOperation),
        CallContract(CallContractOperation),
        ForkContract(ForkContractOperation),
        UpgradeContract(UpgradeContractOperation),
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationCreateContractResult {
        pub op_type: OperationType,
        pub contract_address: String, // Address of the newly created contract
        pub code_hash: String,        // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationCallContractResult {
        pub op_type: OperationType,
        pub return_data: serde_json::Value, // Data returned from the contract call
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationForkContractResult {
        pub op_type: OperationType,
        pub contract_address: String, // Address of the newly created contract
        pub code_hash: String,        // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationUpgradeContractResult {
        pub op_type: OperationType,
        pub code_hash: String, // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(untagged)]
    pub enum OperationResult {
        CreateContract(OperationCreateContractResult),
        CallContract(OperationCallContractResult),
        ForkContract(OperationForkContractResult),
        UpgradeContract(OperationUpgradeContractResult),
    }

    impl Default for OperationResult {
        fn default() -> Self {
            // Default to a simple call contract operation result
            OperationResult::CallContract(OperationCallContractResult {
                op_type: OperationType::CallContract,
                return_data: serde_json::Value::Null,
            })
        }
    }

    impl Default for OperationData {
        fn default() -> Self {
            // Default to a simple call contract operation
            OperationData::CallContract(CallContractOperation {
                op_type: OperationType::CallContract,
                contract_address: String::new(),
                function_name: String::new(),
                parameters: Vec::new(),
            })
        }
    }

    #[glue::storage_item]
    pub struct Transaction {
        pub hash: String,
        pub sender: String,
        pub timestamp: u64,
        pub dependent_transaction_hash: String,
        pub op_data: OperationData,
        pub fuel: u64,
        pub public_keys: Vec<String>,
        pub signatures: Vec<String>,

        // Receipt fields
        pub transaction_index: u64,
        pub status: bool,
        pub op_result: OperationResult, // Operation result
        pub block_hash: String,
    }

    #[glue::storage_item]
    pub struct Block {
        pub hash: String,
        pub parent_hash: String,
        pub height: u64,
        pub state_root: String,
        pub transactions_root: String,
        pub receipts_root: String,
        pub local_timestamp: u64,
        pub protocol_timestamp: u64,
        pub slot_id: u8,
        pub proposer_address: String,
        pub public_keys: Vec<String>,
        pub signatures: Vec<String>,
        pub transactions: Vec<Transaction>,
    }

    #[glue::storage]
    pub struct ExplorerDB {
        pub transactions: glue::collections::Map<String, Transaction>,
        pub blocks: glue::collections::Map<String, Block>,
        pub header_chain: glue::collections::Vec<String>, // record block hash
    }

    impl ExplorerDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            self.transactions.bind_index(
                "address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    match &v.op_data {
                        OperationData::CallContract(op) => match op.function_name.as_str() {
                            "transfer" => {
                                vec![
                                    format!("{}-{:0>19}", op.parameters[0].as_str().unwrap(), v.timestamp),
                                    format!("{}-{:0>19}", op.parameters[1].as_str().unwrap(), v.timestamp),
                                ]
                            }
                            "issue" => {
                                vec![format!("{}-{:0>19}", op.parameters[0].as_str().unwrap(), v.timestamp)]
                            }
                            "mint_token" => {
                                vec![format!("{}-{:0>19}", op.parameters[0].as_str().unwrap(), v.timestamp)]
                            }
                            _ => Vec::new(),
                        },
                        _ => Vec::new(),
                    }
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                transactions: glue::collections::Map::new(),
                blocks: glue::collections::Map::new(),
                header_chain: glue::collections::Vec::new(),
            };
            ret.bind_index();
            ret
        }

        #[glue::atomic]
        pub fn insert_block(&mut self, block_json: String) -> anyhow::Result<()> {
            let block: Block = serde_json::from_str(&block_json)?;

            self.blocks.insert(&block.hash, &block);

            // Insert all transactions from the block
            for transaction in &block.transactions {
                self.insert_transaction(transaction)?;
            }

            // Check height and parent hash
            if self.header_chain.len() > 0 {
                let last_block_hash = self.header_chain.last().unwrap();
                let last_block = self.blocks.get(&last_block_hash).unwrap();
                if block.height != last_block.height + 1 {
                    return Err(anyhow::anyhow!(
                        "Block height is not continuous, expected height {}, got height {}",
                        last_block.height + 1,
                        block.height
                    ));
                }
                if block.parent_hash != *last_block_hash {
                    return Err(anyhow::anyhow!(
                        "Block parent hash does not match, expected {}, got {}, may be a fork or reorg",
                        last_block_hash,
                        block.parent_hash
                    ));
                }
            }

            self.header_chain.push(&block.hash);

            Ok(())
        }

        fn insert_transaction(&mut self, transaction: &Transaction) -> anyhow::Result<()> {
            // Insert the transaction with its hash as the key
            self.transactions.insert(&transaction.hash, &transaction);
            Ok(())
        }

        #[glue::readonly]
        pub fn get_address_transactions(
            &self,
            reverse: bool,
            address: String,
            start: u64,
            end: u64,
            limit: u64,
        ) -> anyhow::Result<String> {
            let mut count = 0u64;
            let (start, end) = if !reverse {
                (
                    format!("{}-{:0>19}", address, start),
                    format!("{}-{:9>19}", address, end),
                )
            } else {
                (
                    format!("{}-{:9>19}", address, start),
                    format!("{}-{:0>19}", address, end),
                )
            };

            let mut transactions = vec![];
            let mut iter = self
                .transactions
                .index("address")
                .iter(reverse, &start, &end);

            while iter.next() {
                println!("key: {}, value: {:?}", iter.key()?, iter.value()?);
                transactions.push(iter.value()?);
                count += 1;
                if count >= limit {
                    break;
                }
            }
            Ok(serde_json::to_string(&transactions)?)
        }

        #[glue::readonly]
        pub fn get_block_by_hash(&self, block_hash: String) -> anyhow::Result<String> {
            let block = self.blocks.get(&block_hash);
            match block {
                Some(block) => Ok(serde_json::to_string(&block)?),
                None => Err(anyhow::anyhow!("Block not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_by_hash(&self, transaction_hash: String) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => Ok(serde_json::to_string(&transaction)?),
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_op_data(&self, transaction_hash: String) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => Ok(serde_json::to_string(&transaction.op_data)?),
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_op_result(
            &self,
            transaction_hash: String,
        ) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => {
                    // Directly serialize the operation result
                    Ok(serde_json::to_string(&transaction.op_result)?)
                }
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_synced_block_height(&self) -> anyhow::Result<u64> {
            if self.header_chain.len() == 0 {
                return Err(anyhow::anyhow!("No blocks found"));
            }
            Ok((self.header_chain.len() - 1) as u64)
        }
    }
}
