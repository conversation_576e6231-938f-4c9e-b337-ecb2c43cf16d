#[glue::contract]
mod vcloud_db {
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;
    use std::fmt;

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "String", from = "String")]
    pub enum ServiceStatus {
        Active,
        Inactive,
        Pending,
        Suspended,
    }

    impl fmt::Display for ServiceStatus {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            match self {
                ServiceStatus::Active => write!(f, "active"),
                ServiceStatus::Inactive => write!(f, "inactive"),
                ServiceStatus::Pending => write!(f, "pending"),
                ServiceStatus::Suspended => write!(f, "suspended"),
            }
        }
    }

    impl From<ServiceStatus> for String {
        fn from(status: ServiceStatus) -> String {
            status.to_string()
        }
    }

    impl From<String> for ServiceStatus {
        fn from(value: String) -> Self {
            match value.as_str() {
                "active" => ServiceStatus::Active,
                "inactive" => ServiceStatus::Inactive,
                "pending" => ServiceStatus::Pending,
                "suspended" => ServiceStatus::Suspended,
                _ => panic!("Invalid service status value"),
            }
        }
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct UserService {
        pub id: String,
        pub duration: i64,
        pub amount: f64,
        pub public_key: String,
        pub provider: String,
        pub provider_address: String,
        pub address: String,
        pub service_id: String,
        pub service_activated: bool,
        pub status: ServiceStatus,
        pub service_options: HashMap<String, String>,
        pub created_at: i64,
        pub updated_at: i64,
        pub deleted_at: i64,
    }

    /// Query parameters for filtering user services
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct QueryParams {
        pub service_id: Option<String>,
        pub address: Option<String>,
        pub provider: Option<String>,
        pub status: Option<ServiceStatus>,
        pub created_at_start: Option<i64>,
        pub created_at_end: Option<i64>,
        pub updated_at_start: Option<i64>,
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        pub sort_by: Option<String>,
        pub sort_desc: Option<bool>,
    }

    /// Batch operation result
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct BatchResult {
        pub created: u64,
        pub updated: u64,
        pub deleted: u64,
        pub errors: Vec<String>,
    }

    #[glue::storage]
    pub struct VCloudDB {
        pub user_services: glue::collections::Map<String, UserService>,
    }

    impl VCloudDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            // Index by provider for efficient provider-based queries
            self.user_services.bind_index(
                "provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by address for efficient address-based queries
            self.user_services.bind_index(
                "address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.user_services.bind_index(
                "status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status.to_string(), v.created_at)]
                }),
            );

            // Index by service_id for efficient service_id-based queries
            self.user_services.bind_index(
                "service_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_id, v.created_at)]
                }),
            );

            // Index by created_at for time-based queries
            self.user_services.bind_index(
                "created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by updated_at for time-based queries
            self.user_services.bind_index(
                "updated_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.updated_at)]
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                user_services: glue::collections::Map::new(),
            };
            ret.bind_index();
            ret
        }

        /// Get current timestamp (placeholder - in real implementation this would get actual time)
        fn get_current_timestamp(&self) -> i64 {
            // In a real implementation, this would get the current blockchain timestamp
            // For now, we'll use a placeholder
            **********
        }

        /// Create a new user service
        #[glue::atomic]
        pub fn create_user_service(&mut self, mut service: UserService) -> anyhow::Result<String> {
            if self.user_services.contains(&service.id) {
                return Err(anyhow::anyhow!("User service with this ID already exists"));
            }

            let current_time = self.get_current_timestamp();
            service.created_at = current_time;
            service.updated_at = current_time;
            service.deleted_at = 0;

            self.user_services.insert(&service.id, &service);
            Ok(service.id)
        }

        /// Batch create user services
        #[glue::atomic]
        pub fn batch_create_user_services(&mut self, services: Vec<UserService>) -> anyhow::Result<String> {
            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for mut service in services {
                if self.user_services.contains(&service.id) {
                    result.errors.push(format!("Service with ID {} already exists", service.id));
                    continue;
                }

                service.created_at = current_time;
                service.updated_at = current_time;
                service.deleted_at = 0;

                self.user_services.insert(&service.id, &service);
                result.created += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Get a single user service by ID
        #[glue::readonly]
        pub fn get_user_service(&self, id: String) -> anyhow::Result<String> {
            let service = self.user_services.get(&id);
            match service {
                Some(service) => Ok(serde_json::to_string(&service)?),
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Update an existing user service
        #[glue::atomic]
        pub fn update_user_service(&mut self, mut service: UserService) -> anyhow::Result<()> {
            if !self.user_services.contains(&service.id) {
                return Err(anyhow::anyhow!("User service not found"));
            }

            service.updated_at = self.get_current_timestamp();
            self.user_services.insert(&service.id, &service);
            Ok(())
        }

        /// Batch update user services (specifically for runtime duration updates)
        #[glue::atomic]
        pub fn batch_update_user_services(&mut self, updates: Vec<(String, i64)>) -> anyhow::Result<String> {
            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for (service_id, new_duration) in updates {
                match self.user_services.get(&service_id) {
                    Some(mut service) => {
                        service.duration = new_duration;
                        service.updated_at = current_time;
                        self.user_services.insert(&service_id, &service);
                        result.updated += 1;
                    }
                    None => {
                        result.errors.push(format!("Service with ID {} not found", service_id));
                    }
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Delete a user service (soft delete by setting deleted_at)
        #[glue::atomic]
        pub fn delete_user_service(&mut self, id: String) -> anyhow::Result<()> {
            match self.user_services.get(&id) {
                Some(mut service) => {
                    service.deleted_at = self.get_current_timestamp();
                    service.updated_at = service.deleted_at;
                    self.user_services.insert(&id, &service);
                    Ok(())
                }
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Batch upsert user services (insert, update, or delete based on record state)
        #[glue::atomic]
        pub fn batch_upsert_user_services(&mut self, services: Vec<UserService>) -> anyhow::Result<String> {
            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for mut service in services {
                // If deleted_at is set and > 0, this is a delete operation
                if service.deleted_at > 0 {
                    match self.user_services.get(&service.id) {
                        Some(mut existing_service) => {
                            existing_service.deleted_at = current_time;
                            existing_service.updated_at = current_time;
                            self.user_services.insert(&service.id, &existing_service);
                            result.deleted += 1;
                        }
                        None => {
                            result.errors.push(format!("Service with ID {} not found for deletion", service.id));
                        }
                    }
                } else if self.user_services.contains(&service.id) {
                    // Update existing service
                    service.updated_at = current_time;
                    self.user_services.insert(&service.id, &service);
                    result.updated += 1;
                } else {
                    // Create new service
                    service.created_at = current_time;
                    service.updated_at = current_time;
                    service.deleted_at = 0;
                    self.user_services.insert(&service.id, &service);
                    result.created += 1;
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Enhanced query user services with comprehensive filtering, pagination, and sorting
        #[glue::readonly]
        pub fn query_user_services_advanced(&self, params_json: String) -> anyhow::Result<String> {
            let params: QueryParams = serde_json::from_str(&params_json)?;

            let mut services = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(100);
            let offset = params.offset.unwrap_or(0);

            // Determine the most efficient index to use based on provided filters
            if let Some(ref address) = params.address {
                // Use address index
                let start_key = format!("{}-{:0>19}", address, 0);
                let end_key = format!("{}-{:9>19}", address, i64::MAX);

                let mut iter = self.user_services.index("address").iter(false, &start_key, &end_key);
                while iter.next() {
                    if count < offset {
                        count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }

                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        services.push(service);
                    }
                    count += 1;
                }
            } else if let Some(ref provider) = params.provider {
                // Use provider index
                let start_key = format!("{}-{:0>19}", provider, 0);
                let end_key = format!("{}-{:9>19}", provider, i64::MAX);

                let mut iter = self.user_services.index("provider").iter(false, &start_key, &end_key);
                while iter.next() {
                    if count < offset {
                        count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }

                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        services.push(service);
                    }
                    count += 1;
                }
            } else if let Some(ref status) = params.status {
                // Use status index
                let start_key = format!("{}-{:0>19}", status.to_string(), 0);
                let end_key = format!("{}-{:9>19}", status.to_string(), i64::MAX);

                let mut iter = self.user_services.index("status").iter(false, &start_key, &end_key);
                while iter.next() {
                    if count < offset {
                        count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }

                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        services.push(service);
                    }
                    count += 1;
                }
            } else if let Some(ref service_id) = params.service_id {
                // Use service_id index
                let start_key = format!("{}-{:0>19}", service_id, 0);
                let end_key = format!("{}-{:9>19}", service_id, i64::MAX);

                let mut iter = self.user_services.index("service_id").iter(false, &start_key, &end_key);
                while iter.next() {
                    if count < offset {
                        count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }

                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        services.push(service);
                    }
                    count += 1;
                }
            } else {
                // No specific index, iterate through all services
                // This is less efficient but handles cases with only time-based or no filters
                let mut iter = self.user_services.iter();
                while iter.next() {
                    if count < offset {
                        count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }

                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        services.push(service);
                    }
                    count += 1;
                }
            }

            // Apply sorting if requested
            if let Some(ref sort_by) = params.sort_by {
                let sort_desc = params.sort_desc.unwrap_or(false);
                self.sort_services(&mut services, sort_by, sort_desc);
            }

            Ok(serde_json::to_string(&services)?)
        }

        /// Simple query function for backward compatibility
        #[glue::readonly]
        pub fn query_user_services(&self, address: Option<String>, status: Option<ServiceStatus>, limit: u64) -> anyhow::Result<String> {
            let params = QueryParams {
                service_id: None,
                address,
                provider: None,
                status,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: Some(limit),
                sort_by: None,
                sort_desc: None,
            };

            let params_json = serde_json::to_string(&params)?;
            self.query_user_services_advanced(params_json)
        }

        /// Helper function to check if a service matches the given filters
        fn matches_filters(&self, service: &UserService, params: &QueryParams) -> bool {
            // Skip deleted services unless specifically querying for them
            if service.deleted_at > 0 {
                return false;
            }

            // Check service_id filter
            if let Some(ref service_id) = params.service_id {
                if service.service_id != *service_id {
                    return false;
                }
            }

            // Check address filter
            if let Some(ref address) = params.address {
                if service.address != *address {
                    return false;
                }
            }

            // Check provider filter
            if let Some(ref provider) = params.provider {
                if service.provider != *provider {
                    return false;
                }
            }

            // Check status filter
            if let Some(ref status) = params.status {
                if service.status != *status {
                    return false;
                }
            }

            // Check created_at time range
            if let Some(start) = params.created_at_start {
                if service.created_at < start {
                    return false;
                }
            }
            if let Some(end) = params.created_at_end {
                if service.created_at > end {
                    return false;
                }
            }

            // Check updated_at time range
            if let Some(start) = params.updated_at_start {
                if service.updated_at < start {
                    return false;
                }
            }
            if let Some(end) = params.updated_at_end {
                if service.updated_at > end {
                    return false;
                }
            }

            true
        }

        /// Helper function to sort services by the specified field
        fn sort_services(&self, services: &mut Vec<UserService>, sort_by: &str, desc: bool) {
            match sort_by {
                "created_at" => {
                    if desc {
                        services.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                    } else {
                        services.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                    }
                }
                "updated_at" => {
                    if desc {
                        services.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
                    } else {
                        services.sort_by(|a, b| a.updated_at.cmp(&b.updated_at));
                    }
                }
                "amount" => {
                    if desc {
                        services.sort_by(|a, b| b.amount.partial_cmp(&a.amount).unwrap_or(std::cmp::Ordering::Equal));
                    } else {
                        services.sort_by(|a, b| a.amount.partial_cmp(&b.amount).unwrap_or(std::cmp::Ordering::Equal));
                    }
                }
                "duration" => {
                    if desc {
                        services.sort_by(|a, b| b.duration.cmp(&a.duration));
                    } else {
                        services.sort_by(|a, b| a.duration.cmp(&b.duration));
                    }
                }
                _ => {
                    // Default sort by created_at
                    if desc {
                        services.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                    } else {
                        services.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                    }
                }
            }
        }
    }

    #[cfg(test)]
    mod tests {
        use super::*;
        use crate::vcloud_db::{self, VCloudDB};

        fn create_test_user_service() -> UserService {
            UserService {
                id: "test_id".to_string(),
                duration: 3600,
                amount: 100.0,
                public_key: "test_public_key".to_string(),
                provider: "test_provider".to_string(),
                provider_address: "test_provider_address".to_string(),
                address: "test_address".to_string(),
                service_id: "test_service_id".to_string(),
                service_activated: true,
                status: ServiceStatus::Active,
                service_options: {
                    let mut map = HashMap::new();
                    map.insert("option1".to_string(), "value1".to_string());
                    map
                },
                created_at: **********,
                updated_at: **********,
                deleted_at: 0,
            }
        }

        fn create_test_service_with_id(id: &str) -> UserService {
            let mut service = create_test_user_service();
            service.id = id.to_string();
            service
        }

        #[glue::test]
        fn test_create_user_service() {
            println!("\n=== Testing create_user_service ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();
            let service = create_test_user_service();

            println!("Creating first service...");
            let result = db.create_user_service(service.clone());
            println!("First creation result: {:?}", result);
            assert!(result.is_ok());

            println!("Attempting to create duplicate service...");
            let result = db.create_user_service(service);
            println!("Second creation result: {:?}", result);
            assert!(result.is_err());
        }

        #[glue::test]
        fn test_batch_create_user_services() {
            println!("\n=== Testing batch_create_user_services ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();

            let services = vec![
                create_test_service_with_id("batch_1"),
                create_test_service_with_id("batch_2"),
                create_test_service_with_id("batch_3"),
            ];

            println!("Creating batch of {} services...", services.len());
            let result = db.batch_create_user_services(services);
            assert!(result.is_ok(), "Failed to batch create services: {:?}", result.err());

            let result_json = result.unwrap();
            let batch_result: BatchResult = serde_json::from_str(&result_json).unwrap();
            println!("Batch result: {:?}", batch_result);

            assert_eq!(batch_result.created, 3);
            assert_eq!(batch_result.updated, 0);
            assert_eq!(batch_result.deleted, 0);
            assert_eq!(batch_result.errors.len(), 0);

            // Verify services were created
            for i in 1..=3 {
                let id = format!("batch_{}", i);
                let get_result = db.get_user_service(id);
                assert!(get_result.is_ok(), "Failed to get service after batch creation");
            }
        }

        #[glue::test]
        fn test_batch_update_user_services() {
            println!("\n=== Testing batch_update_user_services ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();

            // Create some services first
            let services = vec![
                create_test_service_with_id("update_1"),
                create_test_service_with_id("update_2"),
            ];

            for service in services {
                db.create_user_service(service).unwrap();
            }

            // Batch update durations
            let updates = vec![
                ("update_1".to_string(), 7200i64),
                ("update_2".to_string(), 10800i64),
                ("nonexistent".to_string(), 3600i64), // This should fail
            ];

            let result = db.batch_update_user_services(updates);
            assert!(result.is_ok(), "Failed to batch update services: {:?}", result.err());

            let result_json = result.unwrap();
            let batch_result: BatchResult = serde_json::from_str(&result_json).unwrap();
            println!("Batch update result: {:?}", batch_result);

            assert_eq!(batch_result.updated, 2);
            assert_eq!(batch_result.errors.len(), 1);

            // Verify updates
            let service1 = db.get_user_service("update_1".to_string()).unwrap();
            let parsed_service1: UserService = serde_json::from_str(&service1).unwrap();
            assert_eq!(parsed_service1.duration, 7200);

            let service2 = db.get_user_service("update_2".to_string()).unwrap();
            let parsed_service2: UserService = serde_json::from_str(&service2).unwrap();
            assert_eq!(parsed_service2.duration, 10800);
        }

        #[glue::test]
        fn test_batch_upsert_user_services() {
            println!("\n=== Testing batch_upsert_user_services ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();

            // Create one service first
            let existing_service = create_test_service_with_id("upsert_existing");
            db.create_user_service(existing_service.clone()).unwrap();

            // Prepare upsert batch: update existing, create new, delete existing
            let mut update_service = existing_service.clone();
            update_service.amount = 200.0;

            let new_service = create_test_service_with_id("upsert_new");

            let mut delete_service = existing_service.clone();
            delete_service.id = "upsert_existing".to_string();
            delete_service.deleted_at = 1; // Mark for deletion

            let upsert_services = vec![update_service, new_service, delete_service];

            let result = db.batch_upsert_user_services(upsert_services);
            assert!(result.is_ok(), "Failed to batch upsert services: {:?}", result.err());

            let result_json = result.unwrap();
            let batch_result: BatchResult = serde_json::from_str(&result_json).unwrap();
            println!("Batch upsert result: {:?}", batch_result);

            assert_eq!(batch_result.created, 1);
            assert_eq!(batch_result.updated, 0); // The update was overridden by delete
            assert_eq!(batch_result.deleted, 1);

            // Verify new service was created
            let new_result = db.get_user_service("upsert_new".to_string());
            assert!(new_result.is_ok(), "New service should be created");

            // Verify existing service was deleted (soft delete)
            let existing_result = db.get_user_service("upsert_existing".to_string());
            if existing_result.is_ok() {
                let service_json = existing_result.unwrap();
                let service: UserService = serde_json::from_str(&service_json).unwrap();
                assert!(service.deleted_at > 0, "Service should be soft deleted");
            }
        }

        #[glue::test]
        fn test_query_user_services_advanced() {
            println!("\n=== Testing query_user_services_advanced ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();

            // Create test services with different attributes
            let mut services = Vec::new();

            for i in 1..=5 {
                let mut service = create_test_service_with_id(&format!("query_test_{}", i));
                service.address = format!("address_{}", if i <= 3 { 1 } else { 2 });
                service.provider = format!("provider_{}", if i <= 2 { 1 } else { 2 });
                service.status = if i % 2 == 0 { ServiceStatus::Active } else { ServiceStatus::Inactive };
                service.amount = (i as f64) * 100.0;
                service.duration = (i as i64) * 1000;
                services.push(service);
            }

            // Create all services
            for service in services {
                db.create_user_service(service).unwrap();
            }

            // Test 1: Query with address filter
            let params = QueryParams {
                address: Some("address_1".to_string()),
                service_id: None,
                provider: None,
                status: None,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: Some(10),
                sort_by: None,
                sort_desc: None,
            };

            let params_json = serde_json::to_string(&params).unwrap();
            let result = db.query_user_services_advanced(params_json);
            assert!(result.is_ok(), "Failed to query with address filter: {:?}", result.err());

            let services_json = result.unwrap();
            let filtered_services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            println!("Found {} services with address_1", filtered_services.len());
            assert_eq!(filtered_services.len(), 3); // Services 1, 2, 3

            // Test 2: Query with status filter
            let params = QueryParams {
                address: None,
                service_id: None,
                provider: None,
                status: Some(ServiceStatus::Active),
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: Some(10),
                sort_by: None,
                sort_desc: None,
            };

            let params_json = serde_json::to_string(&params).unwrap();
            let result = db.query_user_services_advanced(params_json);
            assert!(result.is_ok(), "Failed to query with status filter: {:?}", result.err());

            let services_json = result.unwrap();
            let filtered_services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            println!("Found {} active services", filtered_services.len());
            assert_eq!(filtered_services.len(), 2); // Services 2, 4

            // Test 3: Query with sorting
            let params = QueryParams {
                address: None,
                service_id: None,
                provider: None,
                status: None,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: Some(10),
                sort_by: Some("amount".to_string()),
                sort_desc: Some(true),
            };

            let params_json = serde_json::to_string(&params).unwrap();
            let result = db.query_user_services_advanced(params_json);
            assert!(result.is_ok(), "Failed to query with sorting: {:?}", result.err());

            let services_json = result.unwrap();
            let sorted_services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            println!("Found {} services sorted by amount desc", sorted_services.len());

            // Verify sorting (should be 500, 400, 300, 200, 100)
            for i in 0..sorted_services.len()-1 {
                assert!(sorted_services[i].amount >= sorted_services[i+1].amount,
                    "Services not sorted correctly by amount desc");
            }
        }

        #[glue::test]
        fn test_update_user_service() {
            println!("\n=== Testing update_user_service ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();
            let mut service = create_test_user_service();

            db.create_user_service(service.clone()).unwrap();

            service.status = ServiceStatus::Suspended;
            assert!(db.update_user_service(service.clone()).is_ok());

            let updated_service = db.get_user_service(service.id.clone()).unwrap();
            let parsed_service: UserService = serde_json::from_str(&updated_service).unwrap();
            assert_eq!(parsed_service.status, ServiceStatus::Suspended);

            let non_existent_service = UserService {
                id: "non_existent".to_string(),
                ..service
            };
            assert!(db.update_user_service(non_existent_service).is_err());
        }

        #[glue::test]
        fn test_get_user_service() {
            println!("\n=== Testing get_user_service ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();
            let service = create_test_user_service();

            db.create_user_service(service.clone()).unwrap();

            let retrieved_service = db.get_user_service(service.id.clone()).unwrap();
            let parsed_service: UserService = serde_json::from_str(&retrieved_service).unwrap();
            assert_eq!(parsed_service.id, service.id);

            assert!(db.get_user_service("non_existent".to_string()).is_err());
        }

        #[glue::test]
        fn test_delete_user_service() {
            println!("\n=== Testing delete_user_service ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();
            let service = create_test_user_service();

            db.create_user_service(service.clone()).unwrap();

            // Delete the service
            let delete_result = db.delete_user_service(service.id.clone());
            assert!(delete_result.is_ok(), "Failed to delete service: {:?}", delete_result.err());

            // Service should still exist but with deleted_at set
            let get_result = db.get_user_service(service.id.clone());
            assert!(get_result.is_ok(), "Service should still exist after soft delete");

            let service_json = get_result.unwrap();
            let deleted_service: UserService = serde_json::from_str(&service_json).unwrap();
            assert!(deleted_service.deleted_at > 0, "Service should have deleted_at timestamp");

            // Try to delete non-existent service
            assert!(db.delete_user_service("non_existent".to_string()).is_err());
        }

        #[glue::test]
        fn test_query_user_services_backward_compatibility() {
            println!("\n=== Testing query_user_services (backward compatibility) ===");
            vcloud_db::set_instance(VCloudDB::new());
            let db = vcloud_db::get_instance();

            // Create test services
            let addresses = vec!["compat_addr_1".to_string(), "compat_addr_2".to_string()];
            let statuses = vec![ServiceStatus::Active, ServiceStatus::Inactive];

            for (i, address) in addresses.iter().enumerate() {
                for (j, status) in statuses.iter().enumerate() {
                    let mut service = create_test_service_with_id(&format!("compat_{}_{}", i, j));
                    service.address = address.clone();
                    service.status = status.clone();
                    db.create_user_service(service).unwrap();
                }
            }

            // Test with no filters
            let result = db.query_user_services(None, None, 10);
            assert!(result.is_ok(), "Failed to query services: {:?}", result.err());
            let services_json = result.unwrap();
            let services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            assert_eq!(services.len(), 4, "Should find all 4 services");

            // Test with address filter
            let result = db.query_user_services(Some("compat_addr_1".to_string()), None, 10);
            assert!(result.is_ok(), "Failed to query with address filter: {:?}", result.err());
            let services_json = result.unwrap();
            let services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            assert_eq!(services.len(), 2, "Should find 2 services with address filter");

            // Test with status filter
            let result = db.query_user_services(None, Some(ServiceStatus::Active), 10);
            assert!(result.is_ok(), "Failed to query with status filter: {:?}", result.err());
            let services_json = result.unwrap();
            let services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            assert_eq!(services.len(), 2, "Should find 2 active services");

            // Test with both filters
            let result = db.query_user_services(Some("compat_addr_1".to_string()), Some(ServiceStatus::Active), 10);
            assert!(result.is_ok(), "Failed to query with both filters: {:?}", result.err());
            let services_json = result.unwrap();
            let services: Vec<UserService> = serde_json::from_str(&services_json).unwrap();
            assert_eq!(services.len(), 1, "Should find 1 service with both filters");
        }
    }
}