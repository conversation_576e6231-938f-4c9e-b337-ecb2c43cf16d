#[glue::contract]
mod vcloud_db {
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;
    use std::fmt;

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "String", from = "String")]
    pub enum ServiceStatus {
        Active,
        Inactive,
        Pending,
        Suspended,
    }

    impl fmt::Display for ServiceStatus {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            match self {
                ServiceStatus::Active => write!(f, "active"),
                ServiceStatus::Inactive => write!(f, "inactive"),
                ServiceStatus::Pending => write!(f, "pending"),
                ServiceStatus::Suspended => write!(f, "suspended"),
            }
        }
    }

    impl From<ServiceStatus> for String {
        fn from(status: ServiceStatus) -> String {
            status.to_string()
        }
    }

    impl From<String> for ServiceStatus {
        fn from(value: String) -> Self {
            match value.as_str() {
                "active" => ServiceStatus::Active,
                "inactive" => ServiceStatus::Inactive,
                "pending" => ServiceStatus::Pending,
                "suspended" => ServiceStatus::Suspended,
                _ => ServiceStatus::Inactive, // Default to inactive for invalid values
            }
        }
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct UserService {
        pub id: String,
        pub duration: i64,
        pub amount: f64,
        pub public_key: String,
        pub provider: String,
        pub provider_address: String,
        pub address: String,
        pub service_id: String,
        pub service_activated: bool,
        pub status: ServiceStatus,
        pub service_options: HashMap<String, String>,
        pub created_at: i64,
        pub updated_at: i64,
        pub deleted_at: i64,
    }

    /// Query parameters for filtering user services
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct QueryParams {
        pub service_id: Option<String>,
        pub address: Option<String>,
        pub provider: Option<String>,
        pub status: Option<ServiceStatus>,
        pub created_at_start: Option<i64>,
        pub created_at_end: Option<i64>,
        pub updated_at_start: Option<i64>,
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        pub sort_by: Option<String>,
        pub sort_desc: Option<bool>,
    }

    /// Batch operation result
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct BatchResult {
        pub created: u64,
        pub updated: u64,
        pub deleted: u64,
        pub errors: Vec<String>,
    }

    #[glue::storage]
    pub struct VCloudDB {
        pub user_services: glue::collections::Map<String, UserService>,
    }

    impl VCloudDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            // Index by provider for efficient provider-based queries
            self.user_services.bind_index(
                "provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by address for efficient address-based queries
            self.user_services.bind_index(
                "address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.user_services.bind_index(
                "status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status.to_string(), v.created_at)]
                }),
            );

            // Index by service_id for efficient service_id-based queries
            self.user_services.bind_index(
                "service_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_id, v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.user_services.bind_index(
                "address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status.to_string(), v.created_at)]
                }),
            );

            // Composite index: provider + status for efficient combined queries
            self.user_services.bind_index(
                "provider_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.provider, v.status.to_string(), v.created_at)]
                }),
            );

            // Index by created_at for time-based queries
            self.user_services.bind_index(
                "created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by updated_at for time-based queries
            self.user_services.bind_index(
                "updated_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.updated_at)]
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                user_services: glue::collections::Map::new(),
            };
            ret.bind_index();
            ret
        }

        /// Get current timestamp (placeholder - in real implementation this would get actual time)
        fn get_current_timestamp(&self) -> i64 {
            // In a real implementation, this would get the current blockchain timestamp
            // For now, we'll use a placeholder
            1234567890
        }

        /// Create a new user service
        #[glue::atomic]
        pub fn create_user_service(&mut self, service: UserService) -> anyhow::Result<String> {
            let mut service = service;
            if self.user_services.contains(&service.id) {
                return Err(anyhow::anyhow!("User service with this ID already exists"));
            }

            let current_time = self.get_current_timestamp();
            service.created_at = current_time;
            service.updated_at = current_time;
            service.deleted_at = 0;

            self.user_services.insert(&service.id, &service);
            Ok(service.id)
        }

        /// Batch create user services
        #[glue::atomic]
        pub fn batch_create_user_services(&mut self, services: Vec<UserService>) -> anyhow::Result<String> {
            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for mut service in services {
                if self.user_services.contains(&service.id) {
                    result.errors.push(format!("Service with ID {} already exists", service.id));
                    continue;
                }

                service.created_at = current_time;
                service.updated_at = current_time;
                service.deleted_at = 0;

                self.user_services.insert(&service.id, &service);
                result.created += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Get a single user service by ID
        #[glue::readonly]
        pub fn get_user_service(&self, id: String) -> anyhow::Result<String> {
            let service = self.user_services.get(&id);
            match service {
                Some(service) => Ok(serde_json::to_string(&service)?),
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Update an existing user service
        #[glue::atomic]
        pub fn update_user_service(&mut self, service: UserService) -> anyhow::Result<()> {
            let mut service = service;
            if !self.user_services.contains(&service.id) {
                return Err(anyhow::anyhow!("User service not found"));
            }

            service.updated_at = self.get_current_timestamp();
            self.user_services.insert(&service.id, &service);
            Ok(())
        }

        /// Batch update user services (specifically for runtime duration updates)
        #[glue::atomic]
        pub fn batch_update_user_services(&mut self, updates: Vec<(String, i64)>) -> anyhow::Result<String> {
            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for (service_id, new_duration) in updates {
                match self.user_services.get(&service_id) {
                    Some(mut service) => {
                        service.duration = new_duration;
                        service.updated_at = current_time;
                        self.user_services.insert(&service_id, &service);
                        result.updated += 1;
                    }
                    None => {
                        result.errors.push(format!("Service with ID {} not found", service_id));
                    }
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Delete a user service (soft delete by setting deleted_at)
        #[glue::atomic]
        pub fn delete_user_service(&mut self, id: String) -> anyhow::Result<()> {
            match self.user_services.get(&id) {
                Some(mut service) => {
                    service.deleted_at = self.get_current_timestamp();
                    service.updated_at = service.deleted_at;
                    self.user_services.insert(&id, &service);
                    Ok(())
                }
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Batch upsert user services (insert, update, or delete based on record state)
        #[glue::atomic]
        pub fn batch_upsert_user_services(&mut self, services: Vec<UserService>) -> anyhow::Result<String> {
            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for mut service in services {
                // If deleted_at is set and > 0, this is a delete operation
                if service.deleted_at > 0 {
                    match self.user_services.get(&service.id) {
                        Some(mut existing_service) => {
                            existing_service.deleted_at = current_time;
                            existing_service.updated_at = current_time;
                            self.user_services.insert(&service.id, &existing_service);
                            result.deleted += 1;
                        }
                        None => {
                            result.errors.push(format!("Service with ID {} not found for deletion", service.id));
                        }
                    }
                } else if self.user_services.contains(&service.id) {
                    // Update existing service
                    service.updated_at = current_time;
                    self.user_services.insert(&service.id, &service);
                    result.updated += 1;
                } else {
                    // Create new service
                    service.created_at = current_time;
                    service.updated_at = current_time;
                    service.deleted_at = 0;
                    self.user_services.insert(&service.id, &service);
                    result.created += 1;
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Enhanced query user services with comprehensive filtering, pagination, and sorting
        #[glue::readonly]
        pub fn query_user_services_advanced(&self, params_json: String) -> anyhow::Result<String> {
            let params: QueryParams = serde_json::from_str(&params_json)?;

            let mut services = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(100);
            let offset = params.offset.unwrap_or(0);

            // Determine the most efficient index to use based on provided filters
            // Priority: Use composite indexes when multiple filters are present
            if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
                // Use composite address_status index for optimal performance
                let start_key = format!("{}-{}-{:0>19}", address, status.to_string(), 0);
                let end_key = format!("{}-{}-{:9>19}", address, status.to_string(), i64::MAX);

                let mut iter = self.user_services.index("address_status").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
                // Use composite provider_status index for optimal performance
                let start_key = format!("{}-{}-{:0>19}", provider, status.to_string(), 0);
                let end_key = format!("{}-{}-{:9>19}", provider, status.to_string(), i64::MAX);

                let mut iter = self.user_services.index("provider_status").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            } else if let Some(ref address) = params.address {
                // Use address index
                let start_key = format!("{}-{:0>19}", address, 0);
                let end_key = format!("{}-{:9>19}", address, i64::MAX);

                let mut iter = self.user_services.index("address").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            } else if let Some(ref provider) = params.provider {
                // Use provider index
                let start_key = format!("{}-{:0>19}", provider, 0);
                let end_key = format!("{}-{:9>19}", provider, i64::MAX);

                let mut iter = self.user_services.index("provider").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            } else if let Some(ref status) = params.status {
                // Use status index
                let start_key = format!("{}-{:0>19}", status.to_string(), 0);
                let end_key = format!("{}-{:9>19}", status.to_string(), i64::MAX);

                let mut iter = self.user_services.index("status").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            } else if let Some(ref service_id) = params.service_id {
                // Use service_id index
                let start_key = format!("{}-{:0>19}", service_id, 0);
                let end_key = format!("{}-{:9>19}", service_id, i64::MAX);

                let mut iter = self.user_services.index("service_id").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            } else {
                // No specific index, use created_at index to iterate through all services
                // This ensures we can iterate through all services efficiently
                let start_key = format!("{:0>19}", 0);
                let end_key = format!("{:9>19}", i64::MAX);

                let mut iter = self.user_services.index("created_at").iter(false, &start_key, &end_key);
                while iter.next() {
                    let service = iter.value()?;
                    if self.matches_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            }

            // Apply sorting if requested
            if let Some(ref sort_by) = params.sort_by {
                let sort_desc = params.sort_desc.unwrap_or(false);
                self.sort_services(&mut services, sort_by, sort_desc);
            }

            Ok(serde_json::to_string(&services)?)
        }

        /// Simple query function for backward compatibility
        #[glue::readonly]
        pub fn query_user_services(&self, address: Option<String>, status: Option<ServiceStatus>, limit: u64) -> anyhow::Result<String> {
            let params = QueryParams {
                service_id: None,
                address,
                provider: None,
                status,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: Some(limit),
                sort_by: None,
                sort_desc: None,
            };

            let params_json = serde_json::to_string(&params)?;
            self.query_user_services_advanced(params_json)
        }

        /// Helper function to check if a service matches the given filters
        fn matches_filters(&self, service: &UserService, params: &QueryParams) -> bool {
            // Skip deleted services unless specifically querying for them
            if service.deleted_at > 0 {
                return false;
            }

            // Check service_id filter
            if let Some(ref service_id) = params.service_id {
                if service.service_id != *service_id {
                    return false;
                }
            }

            // Check address filter
            if let Some(ref address) = params.address {
                if service.address != *address {
                    return false;
                }
            }

            // Check provider filter
            if let Some(ref provider) = params.provider {
                if service.provider != *provider {
                    return false;
                }
            }

            // Check status filter
            if let Some(ref status) = params.status {
                if service.status != *status {
                    return false;
                }
            }

            // Check created_at time range
            if let Some(start) = params.created_at_start {
                if service.created_at < start {
                    return false;
                }
            }
            if let Some(end) = params.created_at_end {
                if service.created_at > end {
                    return false;
                }
            }

            // Check updated_at time range
            if let Some(start) = params.updated_at_start {
                if service.updated_at < start {
                    return false;
                }
            }
            if let Some(end) = params.updated_at_end {
                if service.updated_at > end {
                    return false;
                }
            }

            true
        }

        /// Helper function to sort services by the specified field
        fn sort_services(&self, services: &mut Vec<UserService>, sort_by: &str, desc: bool) {
            match sort_by {
                "created_at" => {
                    if desc {
                        services.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                    } else {
                        services.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                    }
                }
                "updated_at" => {
                    if desc {
                        services.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
                    } else {
                        services.sort_by(|a, b| a.updated_at.cmp(&b.updated_at));
                    }
                }
                "amount" => {
                    if desc {
                        services.sort_by(|a, b| b.amount.partial_cmp(&a.amount).unwrap_or(std::cmp::Ordering::Equal));
                    } else {
                        services.sort_by(|a, b| a.amount.partial_cmp(&b.amount).unwrap_or(std::cmp::Ordering::Equal));
                    }
                }
                "duration" => {
                    if desc {
                        services.sort_by(|a, b| b.duration.cmp(&a.duration));
                    } else {
                        services.sort_by(|a, b| a.duration.cmp(&b.duration));
                    }
                }
                _ => {
                    // Default sort by created_at
                    if desc {
                        services.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                    } else {
                        services.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                    }
                }
            }
        }
    }
}