# ----- server management -----

# max_sessions is used to limit the max sessions of this peer
# type: int
max_sessions: 3000

# max_send_recv is used to limit the max send & recv buffer size of this peer
# type: int (in bytes)
max_send_recv: 5000000

# session_timeout is used to cut off stale sessions.
# type: int (in seconds)
session_timeout: 60

# log_session_interval is used to log session info periodically
# type: int (in seconds)
log_session_interval: 60

# peer_servers are hard coded remote peer service address
# type: list
#peer_servers:
#  - tcp://127.0.0.1:9877

# local_address are local service address
# e.g. tcp://:8080 means the rpc server will listen on all network interfaces on port 8080
# type: list
local_address:
  - tcp://:9877
  - ws://:9977

# internet_address are report service address, by which other peers can find this peer
# type: list
# if you don't specify the ip address, the node will get its public ip address automatically, and use the port you specified
# e.g. tcp://:20002 means the peer can find this node through tcp protocol on port 20002, and the ip address is this node's public ip address
# e.g. tcp://***********:20002 means the peer can find this node through tcp://***********:20002
internet_address:
  - tcp://127.0.0.1:9877

# Swagger is a tool for user-friendly API access
# type: list
swagger: tcp://:9878

# System contracts address json location
# type: string
# This is used to generate contract address autofill feature in Swagger
swagger_system_contracts_address_file: "chains/vgraph_spos_chain/system_contracts.json"

# Contract source code location
# type: string
# This is used to generate contract apis in Swagger
swagger_contract_source_code_dir: "contract/base/source"

# ------ database ------

# db_path is the base path of the database. real path is db_path + '_' + chain_id
# type: string
db_path: vgraph_lmdb_0

# ------ blockchain ------

# chain_id is the chain id of the node
# type: string
# currently supported chain_id:
#  - vgraphspos
#  - vgraphpowprime

chain_id: "vgraphspos"
# chain_id: "vgraphpowprime"

# private_key is the private key of the node
# type: string
# when it's empty, the node will generate a new private key
# you can use scripts/schnorr_generate_keys.py to generate a key pair
private_key: "0x0cf9455dbbb7b26ff02fe76d25f228b0eee8d077f8d6ea786c0cefbd2a91480731729d4ec734c5198f72c9ccda9e0bd62ed512ee3e8b526b35d6db147d9d12a5"

# query_api_key is the api key for query api
# type: optional string
# when it's disabled, query api key authentication is disabled
#query_api_key: "60e687e2-7642-41bf-8ee1-6bda1b0ddb40"

# Default system token of the super node
# Used for mintng/mining and comsuming fuel
token_address: "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3"
