import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()

def create_test_user_service(service_id="test_service_1", address="0xtest_address_1", provider="test_provider_1", status="active"):
    """Create a test UserService with realistic data including all new fields"""
    # Add timestamp to make IDs unique across test runs
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "id": f"{service_id}_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "public_key": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": provider,
        "provider_address": "0xprovider_address_123",
        "address": address,
        "service_id": "service_type_compute",
        "service_activated": True,
        "status": status,
        "service_options": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "created_at": **********,
        "updated_at": **********,
        "deleted_at": 0,
        # New fields with default values
        "end_at": 0,
        "service_activate_ts": 0,
        "service_running_ts": 0,
        "service_abort_ts": 0,
        "service_done_ts": 0,
        "service_refund_ts": 0,
        "service": "compute_service",
        "created_addr": address,
        "label_hash": "0xabcdef**********"
    }


def test_create_user_service():
    """Test creating a new user service"""
    service = create_test_user_service()
    service_json = json.dumps(service)

    # Test successful creation
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None
    assert result == service["id"]

    # Test duplicate ID error
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is not None
    assert "already exists" in str(err)


def test_get_user_service():
    """Test retrieving a user service by ID"""
    service = create_test_user_service("get_test_1")
    service_json = json.dumps(service)

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Test successful retrieval
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["id"] == service["id"]
    assert retrieved_service["amount"] == service["amount"]
    assert retrieved_service["provider"] == service["provider"]

    # Test non-existent service
    result, err = vcloudClient.executeReadOnly("get_user_service", str, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_update_user_service():
    """Test updating an existing user service"""
    service = create_test_user_service("update_test_1")
    service_json = json.dumps(service)

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Update the service
    service["amount"] = 200.0
    service["status"] = "suspended"
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update_user_service", None, updated_service_json)
    assert err is None

    # Verify the update
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 200.0
    assert updated_service["status"] == "suspended"

    # Test updating non-existent service
    non_existent_service = create_test_user_service("non_existent")
    non_existent_json = json.dumps(non_existent_service)
    result, err = vcloudClient.execute("update_user_service", None, non_existent_json)
    assert err is not None
    assert "not found" in str(err)


def test_delete_user_service():
    """Test soft deleting a user service"""
    service = create_test_user_service("delete_test_1")
    service_json = json.dumps(service)

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Delete the service
    result, err = vcloudClient.execute("delete_user_service", None, service["id"])
    assert err is None

    # Verify the service still exists but is marked as deleted
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
    assert err is None
    deleted_service = json.loads(result)
    assert deleted_service["deleted_at"] > 0

    # Test deleting non-existent service
    result, err = vcloudClient.execute("delete_user_service", None, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_batch_create_user_services():
    """Test batch creating multiple user services"""
    services = [
        create_test_user_service("batch_create_1", "0xaddr1", "provider1"),
        create_test_user_service("batch_create_2", "0xaddr2", "provider2"),
        create_test_user_service("batch_create_3", "0xaddr3", "provider3"),
    ]
    services_json = json.dumps(services)

    # Test successful batch creation
    result, err = vcloudClient.execute("batch_create_user_services", str, services_json)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 3
    assert batch_result["updated"] == 0
    assert batch_result["deleted"] == 0
    assert len(batch_result["errors"]) == 0

    # Verify services were created
    for service in services:
        result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
        assert err is None
        retrieved_service = json.loads(result)
        assert retrieved_service["id"] == service["id"]

    # Test batch creation with duplicate IDs
    duplicate_services = [
        services[0],  # Use the same service object (duplicate ID)
        create_test_user_service("batch_create_new"),  # New
    ]
    duplicate_services_json = json.dumps(duplicate_services)
    result, err = vcloudClient.execute("batch_create_user_services", str, duplicate_services_json)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 1  # Only the new one
    assert len(batch_result["errors"]) == 1  # One error for duplicate


def test_batch_update_user_services():
    """Test batch updating user service durations"""
    # Create some services first
    services = [
        create_test_user_service("batch_update_1"),
        create_test_user_service("batch_update_2"),
    ]

    service_ids = []
    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None
        service_ids.append(service["id"])  # Store the actual service ID

    # Batch update durations
    updates = [
        [service_ids[0], 7200],  # Use actual service ID
        [service_ids[1], 10800],  # Use actual service ID
        ["non_existent", 3600],  # This should fail
    ]
    updates_json = json.dumps(updates)

    result, err = vcloudClient.execute("batch_update_user_services", str, updates_json)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["updated"] == 2
    assert len(batch_result["errors"]) == 1

    # Verify updates
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service_ids[0])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["duration"] == 7200


def test_batch_upsert_user_services():
    """Test batch upsert operations (insert, update, delete)"""
    # Create one existing service
    existing_service = create_test_user_service("upsert_existing")
    existing_service_json = json.dumps(existing_service)
    result, err = vcloudClient.execute("create_user_service", str, existing_service_json)
    assert err is None
    existing_service_id = existing_service["id"]

    # Prepare upsert batch
    new_service = create_test_user_service("upsert_new")
    updated_service = existing_service.copy()
    updated_service["amount"] = 300.0
    delete_service = existing_service.copy()
    delete_service["deleted_at"] = 1  # Mark for deletion

    upsert_services = [new_service, updated_service, delete_service]
    upsert_services_json = json.dumps(upsert_services)

    result, err = vcloudClient.execute("batch_upsert_user_services", str, upsert_services_json)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 1
    assert batch_result["deleted"] == 1

    # Verify new service was created
    result, err = vcloudClient.executeReadOnly("get_user_service", str, new_service["id"])
    assert err is None

    # Verify existing service was deleted
    result, err = vcloudClient.executeReadOnly("get_user_service", str, existing_service_id)
    assert err is None
    service = json.loads(result)
    assert service["deleted_at"] > 0


def test_query_user_services_backward_compatibility():
    """Test the backward compatibility query function"""
    # Create test services with different attributes
    services = [
        create_test_user_service("query_1", "0xaddr1", "provider1", "active"),
        create_test_user_service("query_2", "0xaddr1", "provider2", "inactive"),
        create_test_user_service("query_3", "0xaddr2", "provider1", "active"),
        create_test_user_service("query_4", "0xaddr2", "provider2", "suspended"),
    ]

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None

    # Test query with no filters (using new signature: ids, address, service_activated, status, provider_address, limit, offset, sort_desc)
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, None, None, None, 100, 0, None)
    assert err is None
    queried_services = json.loads(result)
    # Should find at least the 4 services we just created
    assert len(queried_services) >= 4

    # Test query with address filter
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "0xaddr1", None, None, None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct address
    addr1_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1":
            addr1_count += 1
    assert addr1_count >= 2

    # Test query with status filter
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, None, "active", None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct status
    active_count = 0
    for service in queried_services:
        if service["status"] == "active":
            active_count += 1
    assert active_count >= 2

    # Test query with both address and status filters
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "0xaddr1", None, "active", None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1
    # Verify all returned services match both criteria
    matching_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1" and service["status"] == "active":
            matching_count += 1
    assert matching_count >= 1

    # Test query with limit
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, None, None, None, 2, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 2


def test_query_user_services_advanced():
    """Test the advanced query function with comprehensive filtering"""
    # Create test services with different attributes
    services = [
        create_test_user_service("adv_query_1", "0xaddr1", "provider1", "active"),
        create_test_user_service("adv_query_2", "0xaddr1", "provider2", "inactive"),
        create_test_user_service("adv_query_3", "0xaddr2", "provider1", "active"),
        create_test_user_service("adv_query_4", "0xaddr2", "provider2", "suspended"),
        create_test_user_service("adv_query_5", "0xaddr3", "provider3", "pending"),
    ]

    # Modify some services for variety
    services[0]["amount"] = 500.0
    services[1]["amount"] = 200.0
    services[2]["amount"] = 300.0
    services[3]["amount"] = 150.0
    services[4]["amount"] = 400.0

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None

    # Test 1: Query with address filter
    query_params = {
        "address": "0xaddr1",
        "service_id": None,
        "provider": None,
        "status": None,
        "created_at_start": None,
        "created_at_end": None,
        "updated_at_start": None,
        "updated_at_end": None,
        "offset": None,
        "limit": 10,
        "sort_by": None,
        "sort_desc": None
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct address
    addr1_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1":
            addr1_count += 1
    assert addr1_count >= 2

    # Test 2: Query with provider filter
    query_params["address"] = None
    query_params["provider"] = "provider1"

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct provider
    provider1_count = 0
    for service in queried_services:
        if service["provider"] == "provider1":
            provider1_count += 1
    assert provider1_count >= 2

    # Test 3: Query with status filter
    query_params["provider"] = None
    query_params["status"] = "active"

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct status
    active_count = 0
    for service in queried_services:
        if service["status"] == "active":
            active_count += 1
    assert active_count >= 2

    # Test 4: Query with sorting by created_at (descending) - only created_at sorting is supported now
    query_params["status"] = None
    query_params["sort_by"] = "created_at"
    query_params["sort_desc"] = True

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 5
    # Verify sorting - check that the first few results are in descending order by created_at
    if len(queried_services) >= 3:
        assert queried_services[0]["created_at"] >= queried_services[1]["created_at"]
        assert queried_services[1]["created_at"] >= queried_services[2]["created_at"]

    # Test 5: Query with pagination
    query_params["sort_by"] = None
    query_params["sort_desc"] = None
    query_params["offset"] = 2
    query_params["limit"] = 2

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 2

    # Test 6: Query with multiple filters
    query_params = {
        "address": "0xaddr1",
        "status": "active",
        "offset": None,
        "limit": 10,
        "sort_by": None,
        "sort_desc": None
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1
    # Verify all returned services match both criteria
    matching_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1" and service["status"] == "active":
            matching_count += 1
    assert matching_count >= 1


def test_query_edge_cases():
    """Test edge cases and error handling for queries"""
    # Test with empty database (using new signature: ids, address, service_activated, status, provider_address, limit, offset, sort_desc)
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, None, None, None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0

    # Test with invalid JSON for advanced query
    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, "invalid_json")
    assert err is not None

    # Test with very large limit
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, None, None, None, 1000000, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0  # Still empty database

    # Create a service and test filtering with non-existent values
    service = create_test_user_service("edge_test_1")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Test query with non-existent address
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "0xnonexistent", None, None, None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0

    # Test query with non-existent status
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, None, "nonexistent", None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0


def test_data_validation():
    """Test data validation and error handling"""
    # Test creating service with invalid data types
    invalid_service = {
        "id": "validation_test",
        "duration": "invalid_duration",  # Should be int
        "amount": "invalid_amount",      # Should be float
        "public_key": "test_key",
        "provider": "test_provider",
        "provider_address": "test_provider_addr",
        "address": "test_address",
        "service_id": "test_service_id",
        "service_activated": "invalid_bool",  # Should be bool
        "status": "active",
        "service_options": "invalid_options",  # Should be dict
        "created_at": **********,
        "updated_at": **********,
        "deleted_at": 0
    }

    # This should fail due to type validation
    invalid_service_json = json.dumps(invalid_service)
    result, err = vcloudClient.execute("create_user_service", str, invalid_service_json)
    assert err is not None

    # Test with missing required fields
    incomplete_service = {
        "id": "incomplete_test",
        "duration": 3600,
        # Missing other required fields
    }
    incomplete_service_json = json.dumps(incomplete_service)

    result, err = vcloudClient.execute("create_user_service", str, incomplete_service_json)
    assert err is not None


def test_service_lifecycle():
    """Test complete service lifecycle: create -> update -> query -> delete"""
    service_id = "lifecycle_test"

    # Step 1: Create service
    service = create_test_user_service(service_id, "0xlifecycle_addr", "lifecycle_provider", "pending")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None
    actual_service_id = result
    assert actual_service_id == service["id"]  # Use the actual ID from the service object

    # Step 2: Verify creation
    result, err = vcloudClient.executeReadOnly("get_user_service", str, actual_service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "pending"
    assert retrieved_service["amount"] == 100.0

    # Step 3: Update service (activate and change amount)
    service["status"] = "active"
    service["amount"] = 250.0
    service["service_activated"] = True
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update_user_service", None, updated_service_json)
    assert err is None

    # Step 4: Verify update
    result, err = vcloudClient.executeReadOnly("get_user_service", str, actual_service_id)
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["status"] == "active"
    assert updated_service["amount"] == 250.0
    assert updated_service["service_activated"] == True

    # Step 5: Query to find the service (using new signature: ids, address, service_activated, status, provider_address, limit, offset, sort_desc)
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "0xlifecycle_addr", None, "active", None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1
    found_service = None
    for svc in queried_services:
        if svc["id"] == actual_service_id:
            found_service = svc
            break
    assert found_service is not None

    # Step 6: Soft delete the service
    result, err = vcloudClient.execute("delete_user_service", None, actual_service_id)
    assert err is None

    # Step 7: Verify soft deletion
    result, err = vcloudClient.executeReadOnly("get_user_service", str, actual_service_id)
    assert err is None
    deleted_service = json.loads(result)
    assert deleted_service["deleted_at"] > 0

    # Step 8: Verify deleted services are filtered out from queries
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "0xlifecycle_addr", None, "active", None, 10, 0, None)
    assert err is None
    queried_services = json.loads(result)
    # Should not find the deleted service
    found_deleted = False
    for svc in queried_services:
        if svc["id"] == actual_service_id:
            found_deleted = True
            break
    assert not found_deleted


def test_new_fields_storage_and_retrieval():
    """Test that all new fields are properly stored and retrieved"""
    service = create_test_user_service("new_fields_test", "0xnew_addr", "new_provider", "pending")

    # Set specific values for new fields
    service["end_at"] = **********
    service["service_activate_ts"] = **********
    service["service_running_ts"] = **********
    service["service_abort_ts"] = 0
    service["service_done_ts"] = **********
    service["service_refund_ts"] = 0
    service["service"] = "gpu_compute"
    service["created_addr"] = "0xcreator_address"
    service["label_hash"] = "0x123456789abcdef"

    # Create the service
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None
    service_id = result

    # Retrieve and verify all fields
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service_id)
    assert err is None
    retrieved_service = json.loads(result)

    # Verify all new fields are correctly stored
    assert retrieved_service["end_at"] == **********
    assert retrieved_service["service_activate_ts"] == **********
    assert retrieved_service["service_running_ts"] == **********
    assert retrieved_service["service_abort_ts"] == 0
    assert retrieved_service["service_done_ts"] == **********
    assert retrieved_service["service_refund_ts"] == 0
    assert retrieved_service["service"] == "gpu_compute"
    assert retrieved_service["created_addr"] == "0xcreator_address"
    assert retrieved_service["label_hash"] == "0x123456789abcdef"


def test_batch_id_query():
    """Test batch ID query functionality"""
    # Create multiple services
    services = []
    service_ids = []
    for i in range(5):
        service = create_test_user_service(f"batch_id_{i}", f"0xaddr_{i}", f"provider_{i}", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None
        service_ids.append(service["id"])

    # Test batch query with subset of IDs
    query_ids = service_ids[:3]  # Query first 3 services
    query_params = {
        "ids": query_ids,
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)

    # Should find exactly 3 services
    assert len(queried_services) == 3

    # Verify all returned services are in the requested IDs
    returned_ids = [svc["id"] for svc in queried_services]
    for returned_id in returned_ids:
        assert returned_id in query_ids

    # Test batch query with non-existent IDs
    query_params = {
        "ids": ["non_existent_1", "non_existent_2"],
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0

    # Test batch query with mixed existing and non-existent IDs
    mixed_ids = [service_ids[0], "non_existent", service_ids[1]]
    query_params = {
        "ids": mixed_ids,
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 2  # Only existing services should be returned


def test_enhanced_address_status_query_with_pagination():
    """Test enhanced address+status query with pagination and sorting"""
    # Create services with different amounts for sorting
    services_data = [
        ("addr_status_1", "0xtest_addr", "provider1", "active", 500.0),
        ("addr_status_2", "0xtest_addr", "provider2", "active", 300.0),
        ("addr_status_3", "0xtest_addr", "provider3", "active", 700.0),
        ("addr_status_4", "0xtest_addr", "provider4", "inactive", 400.0),
        ("addr_status_5", "0xother_addr", "provider5", "active", 600.0),
    ]

    for service_id, address, provider, status, amount in services_data:
        service = create_test_user_service(service_id, address, provider, status)
        service["amount"] = amount
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None

    # Test address + status query with sorting by created_at (descending) - only created_at sorting is supported now
    query_params = {
        "address": "0xtest_addr",
        "status": "active",
        "sort_by": "created_at",
        "sort_desc": True,
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)

    # Should find 3 active services for 0xtest_addr
    active_services = [svc for svc in queried_services if svc["address"] == "0xtest_addr" and svc["status"] == "active"]
    assert len(active_services) >= 3

    # Verify sorting (created_at should be in descending order)
    created_ats = [svc["created_at"] for svc in active_services[:3]]
    assert created_ats[0] >= created_ats[1] >= created_ats[2]

    # Test with pagination
    query_params = {
        "address": "0xtest_addr",
        "status": "active",
        "offset": 1,
        "limit": 2,
        "sort_by": "created_at",
        "sort_desc": True
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)

    # Should get at most 2 services (offset=1, limit=2)
    assert len(queried_services) <= 2


def test_ids_address_service_activated_query():
    """Test IDs + address + service_activated filtering"""
    # Create services with different service_activated values
    services = []
    service_ids = []

    # Create services with service_activated = True
    for i in range(3):
        service = create_test_user_service(f"activated_{i}", "0xtest_addr", f"provider_{i}", "active")
        service["service_activated"] = True
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None
        service_ids.append(service["id"])

    # Create services with service_activated = False
    for i in range(2):
        service = create_test_user_service(f"not_activated_{i}", "0xtest_addr", f"provider_{i+3}", "active")
        service["service_activated"] = False
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None
        service_ids.append(service["id"])

    # Query with IDs + address + service_activated = True
    query_params = {
        "ids": service_ids,
        "address": "0xtest_addr",
        "service_activated": True,
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)

    # Should find only the 3 activated services
    assert len(queried_services) == 3
    for service in queried_services:
        assert service["address"] == "0xtest_addr"
        assert service["service_activated"] == True

    # Query with IDs + address + service_activated = False
    query_params = {
        "ids": service_ids,
        "address": "0xtest_addr",
        "service_activated": False,
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)

    # Should find only the 2 non-activated services
    assert len(queried_services) == 2
    for service in queried_services:
        assert service["address"] == "0xtest_addr"
        assert service["service_activated"] == False


def test_ids_provider_address_query():
    """Test IDs + provider_address filtering"""
    # Create services with different provider addresses
    services = []
    service_ids = []

    provider_addresses = ["0xprovider_1", "0xprovider_2", "0xprovider_1"]

    for i, provider_addr in enumerate(provider_addresses):
        service = create_test_user_service(f"provider_addr_{i}", f"0xaddr_{i}", f"provider_{i}", "active")
        service["provider_address"] = provider_addr
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None
        service_ids.append(service["id"])

    # Query with IDs + provider_address
    query_params = {
        "ids": service_ids,
        "provider_address": "0xprovider_1",
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)

    # Should find 2 services with provider_address = "0xprovider_1"
    assert len(queried_services) == 2
    for service in queried_services:
        assert service["provider_address"] == "0xprovider_1"

    # Query with non-matching provider_address
    query_params = {
        "ids": service_ids,
        "provider_address": "0xnon_existent_provider",
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0


def test_query_edge_cases_with_new_fields():
    """Test edge cases for new query functionality"""
    # Test empty IDs list
    query_params = {
        "ids": [],
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0

    # Test query with only service_activated filter
    service = create_test_user_service("service_activated_only", "0xtest", "provider", "active")
    service["service_activated"] = True
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    query_params = {
        "service_activated": True,
        "limit": 10
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1

    # Verify all returned services have service_activated = True
    for service in queried_services:
        assert service["service_activated"] == True

    # Test query with only provider_address filter
    query_params = {
        "provider_address": "0xprovider_address_123",  # Default provider address from create_test_user_service
        "limit": 5
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1

    # Verify all returned services have the correct provider_address
    for service in queried_services:
        assert service["provider_address"] == "0xprovider_address_123"