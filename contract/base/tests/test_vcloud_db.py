import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()

def create_test_user_service(service_id="test_service_1", address="0xtest_address_1", provider="test_provider_1", status="active"):
    """Create a test UserService with realistic data"""
    # Add timestamp to make IDs unique across test runs
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "id": f"{service_id}_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "public_key": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": provider,
        "provider_address": "0xprovider_address_123",
        "address": address,
        "service_id": "service_type_compute",
        "service_activated": True,
        "status": status,
        "service_options": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "created_at": **********,
        "updated_at": **********,
        "deleted_at": 0
    }


def test_create_user_service():
    """Test creating a new user service"""
    service = create_test_user_service()

    # Test successful creation
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is None
    assert result == service["id"]

    # Test duplicate ID error
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is not None
    assert "already exists" in str(err)


def test_get_user_service():
    """Test retrieving a user service by ID"""
    service = create_test_user_service("get_test_1")

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is None

    # Test successful retrieval
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["id"] == service["id"]
    assert retrieved_service["amount"] == service["amount"]
    assert retrieved_service["provider"] == service["provider"]

    # Test non-existent service
    result, err = vcloudClient.executeReadOnly("get_user_service", str, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_update_user_service():
    """Test updating an existing user service"""
    service = create_test_user_service("update_test_1")

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is None

    # Update the service
    service["amount"] = 200.0
    service["status"] = "suspended"
    result, err = vcloudClient.execute("update_user_service", None, service)
    assert err is None

    # Verify the update
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 200.0
    assert updated_service["status"] == "suspended"

    # Test updating non-existent service
    non_existent_service = create_test_user_service("non_existent")
    result, err = vcloudClient.execute("update_user_service", None, non_existent_service)
    assert err is not None
    assert "not found" in str(err)


def test_delete_user_service():
    """Test soft deleting a user service"""
    service = create_test_user_service("delete_test_1")

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is None

    # Delete the service
    result, err = vcloudClient.execute("delete_user_service", None, service["id"])
    assert err is None

    # Verify the service still exists but is marked as deleted
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
    assert err is None
    deleted_service = json.loads(result)
    assert deleted_service["deleted_at"] > 0

    # Test deleting non-existent service
    result, err = vcloudClient.execute("delete_user_service", None, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_batch_create_user_services():
    """Test batch creating multiple user services"""
    services = [
        create_test_user_service("batch_create_1", "0xaddr1", "provider1"),
        create_test_user_service("batch_create_2", "0xaddr2", "provider2"),
        create_test_user_service("batch_create_3", "0xaddr3", "provider3"),
    ]

    # Test successful batch creation
    result, err = vcloudClient.execute("batch_create_user_services", str, services)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 3
    assert batch_result["updated"] == 0
    assert batch_result["deleted"] == 0
    assert len(batch_result["errors"]) == 0

    # Verify services were created
    for service in services:
        result, err = vcloudClient.executeReadOnly("get_user_service", str, service["id"])
        assert err is None
        retrieved_service = json.loads(result)
        assert retrieved_service["id"] == service["id"]

    # Test batch creation with duplicate IDs
    duplicate_services = [
        services[0],  # Use the same service object (duplicate ID)
        create_test_user_service("batch_create_new"),  # New
    ]
    result, err = vcloudClient.execute("batch_create_user_services", str, duplicate_services)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 1  # Only the new one
    assert len(batch_result["errors"]) == 1  # One error for duplicate


def test_batch_update_user_services():
    """Test batch updating user service durations"""
    # Create some services first
    services = [
        create_test_user_service("batch_update_1"),
        create_test_user_service("batch_update_2"),
    ]

    service_ids = []
    for service in services:
        result, err = vcloudClient.execute("create_user_service", str, service)
        assert err is None
        service_ids.append(service["id"])  # Store the actual service ID

    # Batch update durations
    updates = [
        [service_ids[0], 7200],  # Use actual service ID
        [service_ids[1], 10800],  # Use actual service ID
        ["non_existent", 3600],  # This should fail
    ]

    result, err = vcloudClient.execute("batch_update_user_services", str, updates)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["updated"] == 2
    assert len(batch_result["errors"]) == 1

    # Verify updates
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service_ids[0])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["duration"] == 7200


def test_batch_upsert_user_services():
    """Test batch upsert operations (insert, update, delete)"""
    # Create one existing service
    existing_service = create_test_user_service("upsert_existing")
    result, err = vcloudClient.execute("create_user_service", str, existing_service)
    assert err is None
    existing_service_id = existing_service["id"]

    # Prepare upsert batch
    new_service = create_test_user_service("upsert_new")
    updated_service = existing_service.copy()
    updated_service["amount"] = 300.0
    delete_service = existing_service.copy()
    delete_service["deleted_at"] = 1  # Mark for deletion

    upsert_services = [new_service, updated_service, delete_service]

    result, err = vcloudClient.execute("batch_upsert_user_services", str, upsert_services)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 1
    assert batch_result["deleted"] == 1

    # Verify new service was created
    result, err = vcloudClient.executeReadOnly("get_user_service", str, new_service["id"])
    assert err is None

    # Verify existing service was deleted
    result, err = vcloudClient.executeReadOnly("get_user_service", str, existing_service_id)
    assert err is None
    service = json.loads(result)
    assert service["deleted_at"] > 0


def test_query_user_services_backward_compatibility():
    """Test the backward compatibility query function"""
    # Create test services with different attributes
    services = [
        create_test_user_service("query_1", "0xaddr1", "provider1", "active"),
        create_test_user_service("query_2", "0xaddr1", "provider2", "inactive"),
        create_test_user_service("query_3", "0xaddr2", "provider1", "active"),
        create_test_user_service("query_4", "0xaddr2", "provider2", "suspended"),
    ]

    for service in services:
        result, err = vcloudClient.execute("create_user_service", str, service)
        assert err is None

    # Test query with no filters
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, 100)
    assert err is None
    queried_services = json.loads(result)
    # Should find at least the 4 services we just created
    assert len(queried_services) >= 4

    # Test query with address filter
    result, err = vcloudClient.executeReadOnly("query_user_services", str, "0xaddr1", None, 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct address
    addr1_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1":
            addr1_count += 1
    assert addr1_count >= 2

    # Test query with status filter
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "active", 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct status
    active_count = 0
    for service in queried_services:
        if service["status"] == "active":
            active_count += 1
    assert active_count >= 2

    # Test query with both filters
    result, err = vcloudClient.executeReadOnly("query_user_services", str, "0xaddr1", "active", 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1
    # Verify all returned services match both criteria
    matching_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1" and service["status"] == "active":
            matching_count += 1
    assert matching_count >= 1

    # Test query with limit
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, 2)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 2


def test_query_user_services_advanced():
    """Test the advanced query function with comprehensive filtering"""
    # Create test services with different attributes
    services = [
        create_test_user_service("adv_query_1", "0xaddr1", "provider1", "active"),
        create_test_user_service("adv_query_2", "0xaddr1", "provider2", "inactive"),
        create_test_user_service("adv_query_3", "0xaddr2", "provider1", "active"),
        create_test_user_service("adv_query_4", "0xaddr2", "provider2", "suspended"),
        create_test_user_service("adv_query_5", "0xaddr3", "provider3", "pending"),
    ]

    # Modify some services for variety
    services[0]["amount"] = 500.0
    services[1]["amount"] = 200.0
    services[2]["amount"] = 300.0
    services[3]["amount"] = 150.0
    services[4]["amount"] = 400.0

    for service in services:
        result, err = vcloudClient.execute("create_user_service", str, service)
        assert err is None

    # Test 1: Query with address filter
    query_params = {
        "address": "0xaddr1",
        "service_id": None,
        "provider": None,
        "status": None,
        "created_at_start": None,
        "created_at_end": None,
        "updated_at_start": None,
        "updated_at_end": None,
        "offset": None,
        "limit": 10,
        "sort_by": None,
        "sort_desc": None
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct address
    addr1_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1":
            addr1_count += 1
    assert addr1_count >= 2

    # Test 2: Query with provider filter
    query_params["address"] = None
    query_params["provider"] = "provider1"

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct provider
    provider1_count = 0
    for service in queried_services:
        if service["provider"] == "provider1":
            provider1_count += 1
    assert provider1_count >= 2

    # Test 3: Query with status filter
    query_params["provider"] = None
    query_params["status"] = "active"

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 2
    # Verify all returned services have the correct status
    active_count = 0
    for service in queried_services:
        if service["status"] == "active":
            active_count += 1
    assert active_count >= 2

    # Test 4: Query with sorting by amount (descending)
    query_params["status"] = None
    query_params["sort_by"] = "amount"
    query_params["sort_desc"] = True

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 5
    # Verify sorting - check that the first few results are in descending order
    if len(queried_services) >= 3:
        assert queried_services[0]["amount"] >= queried_services[1]["amount"]
        assert queried_services[1]["amount"] >= queried_services[2]["amount"]

    # Test 5: Query with pagination
    query_params["sort_by"] = None
    query_params["sort_desc"] = None
    query_params["offset"] = 2
    query_params["limit"] = 2

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 2

    # Test 6: Query with multiple filters
    query_params = {
        "address": "0xaddr1",
        "status": "active",
        "offset": None,
        "limit": 10,
        "sort_by": None,
        "sort_desc": None
    }

    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1
    # Verify all returned services match both criteria
    matching_count = 0
    for service in queried_services:
        if service["address"] == "0xaddr1" and service["status"] == "active":
            matching_count += 1
    assert matching_count >= 1


def test_query_edge_cases():
    """Test edge cases and error handling for queries"""
    # Test with empty database
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0

    # Test with invalid JSON for advanced query
    result, err = vcloudClient.executeReadOnly("query_user_services_advanced", str, "invalid_json")
    assert err is not None

    # Test with very large limit
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, None, 1000000)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0  # Still empty database

    # Create a service and test filtering with non-existent values
    service = create_test_user_service("edge_test_1")
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is None

    # Test query with non-existent address
    result, err = vcloudClient.executeReadOnly("query_user_services", str, "0xnonexistent", None, 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0

    # Test query with non-existent status
    result, err = vcloudClient.executeReadOnly("query_user_services", str, None, "nonexistent", 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) == 0


def test_data_validation():
    """Test data validation and error handling"""
    # Test creating service with invalid data types
    invalid_service = {
        "id": "validation_test",
        "duration": "invalid_duration",  # Should be int
        "amount": "invalid_amount",      # Should be float
        "public_key": "test_key",
        "provider": "test_provider",
        "provider_address": "test_provider_addr",
        "address": "test_address",
        "service_id": "test_service_id",
        "service_activated": "invalid_bool",  # Should be bool
        "status": "active",
        "service_options": "invalid_options",  # Should be dict
        "created_at": **********,
        "updated_at": **********,
        "deleted_at": 0
    }

    # This should fail due to type validation
    result, err = vcloudClient.execute("create_user_service", str, invalid_service)
    assert err is not None

    # Test with missing required fields
    incomplete_service = {
        "id": "incomplete_test",
        "duration": 3600,
        # Missing other required fields
    }

    result, err = vcloudClient.execute("create_user_service", str, incomplete_service)
    assert err is not None


def test_service_lifecycle():
    """Test complete service lifecycle: create -> update -> query -> delete"""
    service_id = "lifecycle_test"

    # Step 1: Create service
    service = create_test_user_service(service_id, "0xlifecycle_addr", "lifecycle_provider", "pending")
    result, err = vcloudClient.execute("create_user_service", str, service)
    assert err is None
    actual_service_id = result
    assert actual_service_id == service["id"]  # Use the actual ID from the service object

    # Step 2: Verify creation
    result, err = vcloudClient.executeReadOnly("get_user_service", str, actual_service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "pending"
    assert retrieved_service["amount"] == 100.0

    # Step 3: Update service (activate and change amount)
    service["status"] = "active"
    service["amount"] = 250.0
    service["service_activated"] = True
    result, err = vcloudClient.execute("update_user_service", None, service)
    assert err is None

    # Step 4: Verify update
    result, err = vcloudClient.executeReadOnly("get_user_service", str, actual_service_id)
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["status"] == "active"
    assert updated_service["amount"] == 250.0
    assert updated_service["service_activated"] == True

    # Step 5: Query to find the service
    result, err = vcloudClient.executeReadOnly("query_user_services", str, "0xlifecycle_addr", "active", 10)
    assert err is None
    queried_services = json.loads(result)
    assert len(queried_services) >= 1
    found_service = None
    for svc in queried_services:
        if svc["id"] == actual_service_id:
            found_service = svc
            break
    assert found_service is not None

    # Step 6: Soft delete the service
    result, err = vcloudClient.execute("delete_user_service", None, actual_service_id)
    assert err is None

    # Step 7: Verify soft deletion
    result, err = vcloudClient.executeReadOnly("get_user_service", str, actual_service_id)
    assert err is None
    deleted_service = json.loads(result)
    assert deleted_service["deleted_at"] > 0

    # Step 8: Verify deleted services are filtered out from queries
    result, err = vcloudClient.executeReadOnly("query_user_services", str, "0xlifecycle_addr", "active", 10)
    assert err is None
    queried_services = json.loads(result)
    # Should not find the deleted service
    found_deleted = False
    for svc in queried_services:
        if svc["id"] == actual_service_id:
            found_deleted = True
            break
    assert not found_deleted