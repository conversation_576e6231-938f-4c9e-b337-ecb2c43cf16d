#!/usr/bin/env python3
"""
验证 test_data_validation 修复是否有效
"""

import json
import time
from vm import ContractTester

def test_data_validation_fix():
    """验证数据验证测试的修复"""
    print("🔍 验证 test_data_validation 修复...")
    
    # 初始化合约
    vcloudClient = ContractTester(wasmName="vcloud_db")
    vcloudClient.constructor()
    print("✅ 合约初始化成功")
    
    # 测试 1: 无效 JSON
    print("\n📝 测试 1: 无效 JSON")
    invalid_json = '{"_id": "test", "amount": invalid_number}'
    result, err = vcloudClient.execute("create_user_service", str, invalid_json)
    
    if err is not None and "Failed to parse service JSON" in str(err):
        print("✅ 无效 JSON 测试通过")
    else:
        print(f"❌ 无效 JSON 测试失败: err={err}")
        return False
    
    # 测试 2: 空服务对象
    print("\n📝 测试 2: 空服务对象")
    empty_service = {}
    empty_service_json = json.dumps(empty_service)
    result, err = vcloudClient.execute("create_user_service", str, empty_service_json)
    
    # 这应该成功或者有特定的错误
    if err is None or "empty" in str(err).lower():
        print("✅ 空服务对象测试通过")
    else:
        print(f"❌ 空服务对象测试失败: err={err}")
        return False
    
    # 测试 3: 最小有效服务
    print("\n📝 测试 3: 最小有效服务")
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    minimal_service = {
        "_id": f"minimal_test_service_{unique_suffix}"
    }
    minimal_service_json = json.dumps(minimal_service)
    result, err = vcloudClient.execute("create_user_service", str, minimal_service_json)
    
    if err is None:
        print("✅ 最小有效服务创建成功")
        
        # 验证服务被正确创建
        result, err = vcloudClient.executeReadOnly("get_user_service", str, minimal_service["_id"])
        if err is None:
            retrieved_service = json.loads(result)
            print(f"✅ 服务检索成功: ID={retrieved_service['_id']}")
            
            # 验证默认值
            checks = [
                ("_id", minimal_service["_id"]),
                ("duration", 0),
                ("amount", 0.0),
                ("provider", ""),
                ("serviceActivated", False),
                ("serviceOptions", {}),
            ]
            
            all_checks_passed = True
            for field, expected in checks:
                actual = retrieved_service.get(field)
                if actual == expected:
                    print(f"✅ {field}: {actual} (符合预期)")
                else:
                    print(f"❌ {field}: {actual} (预期: {expected})")
                    all_checks_passed = False
            
            # 验证时间戳
            if retrieved_service.get("createdAt", 0) > 0:
                print(f"✅ createdAt: {retrieved_service['createdAt']} (自动设置)")
            else:
                print(f"❌ createdAt: {retrieved_service.get('createdAt')} (应该 > 0)")
                all_checks_passed = False
                
            if retrieved_service.get("updatedAt", 0) > 0:
                print(f"✅ updatedAt: {retrieved_service['updatedAt']} (自动设置)")
            else:
                print(f"❌ updatedAt: {retrieved_service.get('updatedAt')} (应该 > 0)")
                all_checks_passed = False
            
            return all_checks_passed
        else:
            print(f"❌ 服务检索失败: {err}")
            return False
    else:
        print(f"❌ 最小有效服务创建失败: {err}")
        return False

def test_create_many_validation():
    """测试 create_many 的验证逻辑"""
    print("\n🔍 验证 create_many 功能...")
    
    vcloudClient = ContractTester(wasmName="vcloud_db")
    vcloudClient.constructor()
    
    # 测试无效 JSON
    print("\n📝 测试 create_many 无效 JSON")
    result, err = vcloudClient.execute("create_many", str, "invalid_json")
    if err is not None and "Failed to parse services JSON" in str(err):
        print("✅ create_many 无效 JSON 测试通过")
    else:
        print(f"❌ create_many 无效 JSON 测试失败: err={err}")
        return False
    
    # 测试空数组
    print("\n📝 测试 create_many 空数组")
    result, err = vcloudClient.execute("create_many", str, "[]")
    if err is not None and "Services array cannot be empty" in str(err):
        print("✅ create_many 空数组测试通过")
    else:
        print(f"❌ create_many 空数组测试失败: err={err}")
        return False
    
    # 测试有效的 create_many
    print("\n📝 测试 create_many 有效数据")
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    services = [
        {
            "_id": f"create_many_test_1_{unique_suffix}",
            "duration": 3600,
            "amount": 100.0,
            "provider": "test_provider_1"
        },
        {
            "_id": f"create_many_test_2_{unique_suffix}",
            "duration": 7200,
            "amount": 200.0,
            "provider": "test_provider_2"
        }
    ]
    
    services_json = json.dumps(services)
    result, err = vcloudClient.execute("create_many", str, services_json)
    
    if err is None:
        batch_result = json.loads(result)
        if batch_result.get("created") == 2 and batch_result.get("updated") == 0:
            print("✅ create_many 有效数据测试通过")
            return True
        else:
            print(f"❌ create_many 结果不符合预期: {batch_result}")
            return False
    else:
        print(f"❌ create_many 有效数据测试失败: {err}")
        return False

if __name__ == "__main__":
    try:
        print("🚀 开始验证修复...")
        
        success1 = test_data_validation_fix()
        success2 = test_create_many_validation()
        
        if success1 and success2:
            print("\n🎉 所有验证测试通过！修复成功！")
        else:
            print("\n❌ 部分验证测试失败")
            
    except Exception as e:
        print(f"\n💥 验证异常: {e}")
        import traceback
        traceback.print_exc()
