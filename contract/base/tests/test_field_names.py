#!/usr/bin/env python3
"""
Test script to verify the new camelCase field names work correctly
with the updated vcloud_db smart contract.
"""

import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)

@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()

def create_test_service_new_format():
    """Create a test service using the new camelCase field names"""
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"test_new_format_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": "test_provider",
        "providerAddress": "0xprovider_address_123",
        "address": "0xtest_address",
        "serviceID": "service_type_compute",
        "serviceActivated": True,
        "status": "active",
        "serviceOptions": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "createdAt": 0,  # Should be set by contract
        "updatedAt": 0,  # Should be set by contract
        "deletedAt": 0,
        "endAt": 604800,
        "serviceActivateTS": **********,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "compute_service",
        "createdAddr": "0xtest_address",
        "labelHash": "0xabcdef1234567890"
    }

def test_create_with_new_field_names():
    """Test creating a service with new camelCase field names"""
    service = create_test_service_new_format()
    service_json = json.dumps(service)
    
    print("\n🔍 Input JSON with new field names:")
    print(json.dumps(service, indent=2))
    
    # Test creation
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None, f"Failed to create service: {err}"
    assert result == service["_id"]
    
    print(f"✅ Service created successfully with ID: {result}")

def test_get_with_new_field_names():
    """Test retrieving a service and verify field names"""
    service = create_test_service_new_format()
    service_json = json.dumps(service)
    
    # Create the service
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None
    
    # Retrieve the service
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    assert err is None
    
    retrieved_service = json.loads(result)
    
    print("\n🔍 Retrieved service JSON:")
    print(json.dumps(retrieved_service, indent=2))
    
    # Verify field names are correct
    assert "_id" in retrieved_service
    assert "publicKey" in retrieved_service
    assert "providerAddress" in retrieved_service
    assert "serviceID" in retrieved_service
    assert "serviceActivated" in retrieved_service
    assert "serviceOptions" in retrieved_service
    assert "createdAt" in retrieved_service
    assert "updatedAt" in retrieved_service
    assert "deletedAt" in retrieved_service
    assert "endAt" in retrieved_service
    assert "serviceActivateTS" in retrieved_service
    assert "serviceRunningTS" in retrieved_service
    assert "serviceAbortTS" in retrieved_service
    assert "serviceDoneTS" in retrieved_service
    assert "serviceRefundTS" in retrieved_service
    assert "createdAddr" in retrieved_service
    assert "labelHash" in retrieved_service
    
    # Verify values match
    assert retrieved_service["_id"] == service["_id"]
    assert retrieved_service["amount"] == service["amount"]
    assert retrieved_service["provider"] == service["provider"]
    assert retrieved_service["serviceActivated"] == service["serviceActivated"]
    
    print("✅ All field names verified correctly")

def test_timestamp_handling():
    """Test that timestamp handling works correctly"""
    service = create_test_service_new_format()
    
    # Set timestamps to 0 to test automatic setting
    service["createdAt"] = 0
    service["updatedAt"] = 0
    service["deletedAt"] = 0
    
    service_json = json.dumps(service)
    
    # Create the service
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None
    
    # Retrieve the service
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    assert err is None
    
    retrieved_service = json.loads(result)
    
    print(f"\n🔍 Timestamp handling verification:")
    print(f"   createdAt: {retrieved_service['createdAt']} (should be > 0)")
    print(f"   updatedAt: {retrieved_service['updatedAt']} (should be > 0)")
    print(f"   deletedAt: {retrieved_service['deletedAt']} (should be 0)")
    
    # Verify timestamps were set correctly
    assert retrieved_service["createdAt"] > 0, "createdAt should be set by contract when input is 0"
    assert retrieved_service["updatedAt"] > 0, "updatedAt should be set by contract when input is 0"
    assert retrieved_service["deletedAt"] == 0, "deletedAt should remain 0 for new services"
    
    print("✅ Timestamp handling works correctly")

def test_create_many_with_new_field_names():
    """Test create_many function with new field names"""
    services = [
        create_test_service_new_format(),
        create_test_service_new_format(),
    ]
    
    # Modify IDs to be unique
    services[0]["_id"] = "create_many_test_1"
    services[1]["_id"] = "create_many_test_2"
    
    services_json = json.dumps(services)
    
    print("\n🔍 create_many input JSON:")
    print(json.dumps(services, indent=2))
    
    # Test batch creation
    result, err = vcloudClient.execute("create_many", str, services_json)
    assert err is None, f"Failed to create services: {err}"
    
    batch_result = json.loads(result)
    
    print(f"\n📤 create_many result:")
    print(json.dumps(batch_result, indent=2))
    
    assert batch_result["created"] == 2
    assert batch_result["updated"] == 0
    assert batch_result["deleted"] == 0
    assert len(batch_result["errors"]) == 0
    
    # Verify services were created
    for service in services:
        result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
        assert err is None
        retrieved_service = json.loads(result)
        assert retrieved_service["_id"] == service["_id"]
    
    print("✅ create_many works correctly with new field names")

def test_real_production_data_format():
    """Test with real production data format"""
    real_production_service = {
        "_id": "682548304adc165db31acae3",
        "createdAt": 0,  # Test timestamp handling
        "updatedAt": 0,  # Test timestamp handling
        "duration": 168,
        "endAt": 604800,
        "status": "ServicePending",
        "serviceActivated": True,
        "serviceActivateTS": **********,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "serviceID": "67c17e030f86c60edbc5f547",
        "service": "Log Host",
        "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj",
        "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj",
        "provider": "v-kube-service",
        "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr",
        "serviceOptions": {
            "portSpecification": "",
            "persistStorage": "",
            "resourceUnit": "1-Unit-Resource",
            "region": "Europe"
        },
        "labelHash": "",
        "amount": 1.68,
        "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U",
        "deletedAt": 0
    }
    
    service_json = json.dumps(real_production_service)
    
    print("\n🔍 Real production data with new field names:")
    print(json.dumps(real_production_service, indent=2))
    
    # Test creation
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None, f"Failed to create service with production data: {err}"
    
    # Retrieve and verify
    result, err = vcloudClient.executeReadOnly("get_user_service", str, real_production_service["_id"])
    assert err is None
    
    retrieved_service = json.loads(result)
    
    print("\n📤 Retrieved production service:")
    print(json.dumps(retrieved_service, indent=2))
    
    # Verify key fields
    assert retrieved_service["_id"] == real_production_service["_id"]
    assert retrieved_service["service"] == "Log Host"
    assert retrieved_service["provider"] == "v-kube-service"
    assert retrieved_service["amount"] == 1.68
    assert retrieved_service["serviceOptions"]["region"] == "Europe"
    assert retrieved_service["createdAt"] > 0  # Should be set by contract
    assert retrieved_service["updatedAt"] > 0  # Should be set by contract
    
    print("✅ Real production data works correctly with new field names")

if __name__ == "__main__":
    print("🚀 Testing new camelCase field names in vcloud_db smart contract")
    print("=" * 80)
    
    # Run tests manually for demonstration
    register_contract()
    
    test_create_with_new_field_names()
    test_get_with_new_field_names()
    test_timestamp_handling()
    test_create_many_with_new_field_names()
    test_real_production_data_format()
    
    print("\n✅ All tests passed! New field names are working correctly.")
