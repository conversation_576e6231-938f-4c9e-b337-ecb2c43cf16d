#!/usr/bin/env python3
"""
快速测试 vcloud_db 智能合约的基本功能
"""

import json
import time
from vm import ContractTester

def test_basic_functionality():
    """测试基本的 CRUD 功能"""
    print("🚀 开始测试 vcloud_db 基本功能...")
    
    # 初始化合约
    vcloudClient = ContractTester(wasmName="vcloud_db")
    vcloudClient.constructor()
    print("✅ 合约初始化成功")
    
    # 创建测试服务
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    service = {
        "_id": f"test_basic_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": "test_provider",
        "providerAddress": "0xprovider_address_123",
        "address": "0xtest_address",
        "serviceID": "service_type_compute",
        "serviceActivated": True,
        "status": "active",
        "serviceOptions": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "createdAt": 0,  # 应该被合约自动设置
        "updatedAt": 0,  # 应该被合约自动设置
        "deletedAt": 0,
        "endAt": 604800,
        "serviceActivateTS": **********,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "compute_service",
        "createdAddr": "0xtest_address",
        "labelHash": "0xabcdef1234567890"
    }
    
    # 测试创建
    print(f"📝 测试创建服务: {service['_id']}")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    
    if err:
        print(f"❌ 创建失败: {err}")
        return False
    
    print(f"✅ 创建成功: {result}")
    
    # 测试获取
    print(f"📖 测试获取服务: {service['_id']}")
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    
    if err:
        print(f"❌ 获取失败: {err}")
        return False
    
    retrieved_service = json.loads(result)
    print(f"✅ 获取成功")
    print(f"   ID: {retrieved_service['_id']}")
    print(f"   createdAt: {retrieved_service['createdAt']} (自动设置)")
    print(f"   updatedAt: {retrieved_service['updatedAt']} (自动设置)")
    print(f"   serviceActivated: {retrieved_service['serviceActivated']}")
    print(f"   providerAddress: {retrieved_service['providerAddress']}")
    
    # 验证字段名称
    expected_fields = [
        "_id", "duration", "amount", "publicKey", "provider", "providerAddress",
        "address", "serviceID", "serviceActivated", "status", "serviceOptions",
        "createdAt", "updatedAt", "deletedAt", "endAt", "serviceActivateTS",
        "serviceRunningTS", "serviceAbortTS", "serviceDoneTS", "serviceRefundTS",
        "service", "createdAddr", "labelHash"
    ]
    
    missing_fields = []
    for field in expected_fields:
        if field not in retrieved_service:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 缺少字段: {missing_fields}")
        return False
    
    print("✅ 所有字段名称验证通过")
    
    # 测试时间戳处理
    if retrieved_service["createdAt"] <= 0:
        print("❌ createdAt 应该被自动设置为当前时间戳")
        return False
    
    if retrieved_service["updatedAt"] <= 0:
        print("❌ updatedAt 应该被自动设置为当前时间戳")
        return False
    
    if retrieved_service["deletedAt"] != 0:
        print("❌ deletedAt 应该为 0 (未删除)")
        return False
    
    print("✅ 时间戳处理验证通过")
    
    # 测试更新
    print(f"📝 测试更新服务")
    service["amount"] = 200.0
    service["status"] = "suspended"
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update_user_service", None, updated_service_json)
    
    if err:
        print(f"❌ 更新失败: {err}")
        return False
    
    print("✅ 更新成功")
    
    # 验证更新
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    if err:
        print(f"❌ 获取更新后的服务失败: {err}")
        return False
    
    updated_service = json.loads(result)
    if updated_service["amount"] != 200.0 or updated_service["status"] != "suspended":
        print(f"❌ 更新验证失败: amount={updated_service['amount']}, status={updated_service['status']}")
        return False
    
    print("✅ 更新验证通过")
    
    # 测试删除
    print(f"🗑️ 测试删除服务")
    result, err = vcloudClient.execute("delete_user_service", None, service["_id"])
    
    if err:
        print(f"❌ 删除失败: {err}")
        return False
    
    print("✅ 删除成功")
    
    # 验证软删除
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    if err:
        print(f"❌ 获取删除后的服务失败: {err}")
        return False
    
    deleted_service = json.loads(result)
    if deleted_service["deletedAt"] <= 0:
        print(f"❌ 软删除验证失败: deletedAt={deleted_service['deletedAt']}")
        return False
    
    print("✅ 软删除验证通过")
    
    return True

if __name__ == "__main__":
    try:
        success = test_basic_functionality()
        if success:
            print("\n🎉 所有基本功能测试通过！")
        else:
            print("\n❌ 基本功能测试失败")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
