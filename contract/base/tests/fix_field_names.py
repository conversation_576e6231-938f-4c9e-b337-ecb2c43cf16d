#!/usr/bin/env python3
"""
批量修复 test_vcloud_db.py 中的字段名称
"""

import re

def fix_field_names():
    """批量替换字段名称"""
    
    # 读取文件
    with open('test_vcloud_db.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义字段名映射
    field_mappings = {
        '"id":': '"_id":',
        '"created_at":': '"createdAt":',
        '"updated_at":': '"updatedAt":',
        '"deleted_at":': '"deletedAt":',
        '"end_at":': '"endAt":',
        '"service_activated":': '"serviceActivated":',
        '"service_id":': '"serviceID":',
        '"service_options":': '"serviceOptions":',
        '"provider_address":': '"providerAddress":',
        '"created_addr":': '"createdAddr":',
        '"public_key":': '"publicKey":',
        '"service_activate_ts":': '"serviceActivateTS":',
        '"service_running_ts":': '"serviceRunningTS":',
        '"service_abort_ts":': '"serviceAbortTS":',
        '"service_done_ts":': '"serviceDoneTS":',
        '"service_refund_ts":': '"serviceRefundTS":',
        '"label_hash":': '"labelHash":',
        '"sort_by":': '"sortBy":',
        '"sort_desc":': '"sortDesc":',
        '"created_at_start":': '"createdAtStart":',
        '"created_at_end":': '"createdAtEnd":',
        '"updated_at_start":': '"updatedAtStart":',
        '"updated_at_end":': '"updatedAtEnd":',
    }
    
    # 执行替换
    for old_field, new_field in field_mappings.items():
        content = content.replace(old_field, new_field)
    
    # 特殊处理：查询参数中的字段名
    query_field_mappings = {
        '"service_activated"': '"serviceActivated"',
        '"provider_address"': '"providerAddress"',
        '"service_id"': '"serviceID"',
    }
    
    for old_field, new_field in query_field_mappings.items():
        content = content.replace(old_field, new_field)
    
    # 处理排序字段名
    content = re.sub(r'"sort_by"\s*:\s*"created_at"', '"sortBy": "createdAt"', content)
    content = re.sub(r'"sort_desc"\s*:\s*True', '"sortDesc": True', content)
    content = re.sub(r'"sort_desc"\s*:\s*False', '"sortDesc": False', content)
    content = re.sub(r'"sort_desc"\s*:\s*None', '"sortDesc": None', content)
    
    # 处理断言中的字段访问
    assertion_mappings = {
        '["created_at"]': '["createdAt"]',
        '["updated_at"]': '["updatedAt"]',
        '["deleted_at"]': '["deletedAt"]',
        '["service_activated"]': '["serviceActivated"]',
        '["provider_address"]': '["providerAddress"]',
        '["service_id"]': '["serviceID"]',
        '["service_options"]': '["serviceOptions"]',
        '["public_key"]': '["publicKey"]',
        '["created_addr"]': '["createdAddr"]',
        '["label_hash"]': '["labelHash"]',
        '["end_at"]': '["endAt"]',
        '["service_activate_ts"]': '["serviceActivateTS"]',
        '["service_running_ts"]': '["serviceRunningTS"]',
        '["service_abort_ts"]': '["serviceAbortTS"]',
        '["service_done_ts"]': '["serviceDoneTS"]',
        '["service_refund_ts"]': '["serviceRefundTS"]',
    }
    
    for old_access, new_access in assertion_mappings.items():
        content = content.replace(old_access, new_access)
    
    # 写回文件
    with open('test_vcloud_db.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 字段名称批量替换完成")

if __name__ == "__main__":
    fix_field_names()
