#!/usr/bin/env python3
"""
调试测试问题
"""

import json
import time
import sys
import traceback

def test_basic_import():
    """测试基本导入"""
    try:
        from vm import ContractTester
        print("✅ vm 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ vm 模块导入失败: {e}")
        return False

def test_contract_init():
    """测试合约初始化"""
    try:
        from vm import ContractTester
        vcloudClient = ContractTester(wasmName="vcloud_db")
        print("✅ 合约客户端创建成功")
        
        vcloudClient.constructor()
        print("✅ 合约构造函数调用成功")
        return True, vcloudClient
    except Exception as e:
        print(f"❌ 合约初始化失败: {e}")
        traceback.print_exc()
        return False, None

def test_simple_create():
    """测试简单的创建操作"""
    try:
        success, client = test_contract_init()
        if not success:
            return False
        
        # 创建最简单的服务
        unique_suffix = str(int(time.time() * 1000000))[-6:]
        service = {
            "_id": f"debug_test_{unique_suffix}",
            "duration": 3600,
            "amount": 100.0,
            "publicKey": "0x123",
            "provider": "test_provider",
            "providerAddress": "0xprovider123",
            "address": "0xtest_addr",
            "serviceID": "service123",
            "serviceActivated": True,
            "status": "active",
            "serviceOptions": {"cpu": "4"},
            "createdAt": 0,
            "updatedAt": 0,
            "deletedAt": 0,
            "endAt": 0,
            "serviceActivateTS": 0,
            "serviceRunningTS": 0,
            "serviceAbortTS": 0,
            "serviceDoneTS": 0,
            "serviceRefundTS": 0,
            "service": "compute",
            "createdAddr": "0xtest_addr",
            "labelHash": "0xhash123"
        }
        
        service_json = json.dumps(service)
        print(f"📝 尝试创建服务: {service['_id']}")
        
        result, err = client.execute("create_user_service", str, service_json)
        
        if err:
            print(f"❌ 创建失败: {err}")
            return False
        else:
            print(f"✅ 创建成功: {result}")
            
            # 尝试获取
            result, err = client.executeReadOnly("get_user_service", str, service["_id"])
            if err:
                print(f"❌ 获取失败: {err}")
                return False
            else:
                retrieved = json.loads(result)
                print(f"✅ 获取成功: ID={retrieved['_id']}")
                return True
                
    except Exception as e:
        print(f"❌ 简单创建测试异常: {e}")
        traceback.print_exc()
        return False

def test_pytest_import():
    """测试 pytest 相关导入"""
    try:
        import pytest
        print("✅ pytest 导入成功")
        return True
    except Exception as e:
        print(f"❌ pytest 导入失败: {e}")
        return False

def run_single_test():
    """运行单个测试函数"""
    try:
        # 导入测试文件
        sys.path.insert(0, '.')
        
        # 尝试导入测试模块
        print("📝 尝试导入测试模块...")
        import test_vcloud_db
        print("✅ 测试模块导入成功")
        
        # 尝试运行一个简单的测试
        print("📝 尝试运行 test_create_user_service...")
        test_vcloud_db.register_contract()
        test_vcloud_db.test_create_user_service()
        print("✅ test_create_user_service 运行成功")
        
        return True
    except Exception as e:
        print(f"❌ 单个测试运行失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始调试测试问题...")
    print("=" * 50)
    
    # 测试基本功能
    print("\n1. 测试基本导入...")
    test_basic_import()
    
    print("\n2. 测试 pytest 导入...")
    test_pytest_import()
    
    print("\n3. 测试合约初始化...")
    test_contract_init()
    
    print("\n4. 测试简单创建...")
    test_simple_create()
    
    print("\n5. 测试单个测试函数...")
    run_single_test()
    
    print("\n🏁 调试完成")
