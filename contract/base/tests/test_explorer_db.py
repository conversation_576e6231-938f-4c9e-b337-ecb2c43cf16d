import json

import pytest

from vm import ContractTester

explorerClient = ContractTester(
    wasmName="explorer_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    explorerClient.constructor()


def test_explorer():
    # Process all blocks to add op_data to transactions
    blocksJson = [
        """{"hash":"0x6a406531bfe8c90af3633d1f5938204a6b720b630d3a6b2ac9aec2a3aa067622","height":0,"local_timestamp":0,"parent_hash":"0x00000000000000000000000000000000","proposer_address":"0x00000000000000000000000000000000","protocol_timestamp":0,"public_keys":[],"receipts_root":"0x80da85653af11c0bd5ab2bf5afd9e9e822f15cfd62b8247d5774b6132aa98cef","signatures":[],"slot_id":1,"state_root":"0x881866b9e192e47106ca0671f0941378fe9eda68d24fd9a1cdb18d4244db1387","transactions":[{"block_hash":"0x6a406531bfe8c90af3633d1f5938204a6b720b630d3a6b2ac9aec2a3aa067622","dependent_transaction_hash":"","fuel":1000000,"hash":"0x3ae8a78c3da846a4864d7df717ed36e9a58f358836eca31468407dd93a782263","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"content_slot","op_type":1,"parameters":[1,"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"]},"op_result":{"op_type":1,"return_data":null},"public_keys":[],"sender":"0x00000000000000000000000000000000","signatures":[],"status":true,"timestamp":0,"transaction_index":1}],"transactions_root":"0xf180a58f7e403d14b07767696251330cfad61097bc429d13bc962792b11a6b52"}""",
        """{"hash":"0xa6275485ddc372b4272c9e398c5433b226a8e9444baa15794ba106d5db8d6121","height":1,"local_timestamp":1745893755125198000,"parent_hash":"0x6a406531bfe8c90af3633d1f5938204a6b720b630d3a6b2ac9aec2a3aa067622","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893755357058352,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x5396837678d85a0811b37ce01e886cb988f5da40d67df386f3f961451414b3a8","signatures":["0xaa8dcd3c506aa4bf25793f86622c30f6051aa65afbee58221b712efbe2f3cf7870b110b57787d908daa5feffcbd00ac02701ea621f8a0a6ee91248faff8f548f"],"slot_id":1,"state_root":"0x109de1673c7430cc4dce46cf4b55a1496755adcb285b2b26044367a6df248ded","transactions":[{"block_hash":"0xa6275485ddc372b4272c9e398c5433b226a8e9444baa15794ba106d5db8d6121","dependent_transaction_hash":"","fuel":1000000,"hash":"0xf4febe8565277ac3d520410f5404c65ca24ccb0bb5b13ca33469ab9ad36d3328","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xe0d411d6c7a139aa643bd291327d76fb3f98bee467198460e7ee1790e2ad2b63a79d9be7125cc4af4e4d06d3d2f4813f91a8a7635310bab9f6c6d8e3c039c886"],"status":true,"timestamp":1745893755125198000,"transaction_index":0}],"transactions_root":"0xf6205224573d23c657cc81ca8129113d7acb5de5394e1e5a1b9c24c663d858b5"}""",
        """{"hash":"0x972a6076f31ee7cae8136eaaf53cdc3f7cf2789ae1a0fddde59a87991db21974","height":2,"local_timestamp":1745893770155725000,"parent_hash":"0xa6275485ddc372b4272c9e398c5433b226a8e9444baa15794ba106d5db8d6121","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893770387585352,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xc48a8978cadd46cd5dd4fd517167e6fd57462e7db7951970541ba8a0da0ba918","signatures":["0xb47d0780c6208d335ed9edd6513789743db94ac6a7dc048e57cdf16b5589df515be3cc8e35dbdfbecfc7ea64c9ca8ba91a15b643bf0fa589bea81cdbb38e8684"],"slot_id":1,"state_root":"0x1537c0dcf420cd31a465f3b853e5f53cbe90be371b5521d0037040962024f205","transactions":[{"block_hash":"0x972a6076f31ee7cae8136eaaf53cdc3f7cf2789ae1a0fddde59a87991db21974","dependent_transaction_hash":"","fuel":1000000,"hash":"0x3cb9603ecd3e69c7a1af189ec21a92a284e8e753513343dc001cf25bfc30a0c1","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xf2dd9017c509005ffe59a741785aa2a7698a8dda7d1c05d7878bb944062e6254519d9e981f5be742c075fdb90de9a58644a990b877d75a941aaad67f10da8080"],"status":true,"timestamp":1745893770155725000,"transaction_index":0}],"transactions_root":"0x51790fabf093604d73c5414e1a4a75861f20553e2b744780943667a507b01353"}""",
        """{"hash":"0x3cd9457e80f224e5db5d5b9f6ca95cb94261bc42110b4c638f43624e2b287500","height":3,"local_timestamp":1745893785195411000,"parent_hash":"0x972a6076f31ee7cae8136eaaf53cdc3f7cf2789ae1a0fddde59a87991db21974","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893785411237226,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xa8fc846a376ea580c42c3ece043c6ef3e54619b9414c04d399b28953a2018853","signatures":["0xbe44680959f94c3aec89f444ed23a646a789cccba45426e09a1ebaa06a568b0f03538be788e305722cf2112e52e8a982d9e6fde8dd116c0733ca8edd725f3c8f"],"slot_id":1,"state_root":"0xc1181dd43ab03701004465b1eb66f2e316860ab91dd311ad8045345cec955ebe","transactions":[{"block_hash":"0x3cd9457e80f224e5db5d5b9f6ca95cb94261bc42110b4c638f43624e2b287500","dependent_transaction_hash":"","fuel":1000000,"hash":"0x669884f8ee20d6548ba4a2c0e1582ea4fc3154de3b2b7fc4205baf5ca422eb18","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x562fc6b9d1cbcc6a10a8bd0e4b28afc3f32d90800897d864bb2ab70a25b27e4e319bf8fc37269d53645eeadfb4ed0dabd17ea44ed0fe5e210980529c8c1abf85"],"status":true,"timestamp":1745893785195411000,"transaction_index":0}],"transactions_root":"0xd666b3bf201a5090643a6d25d4bb588434afb82cee140dd2950ba79e1b34d0a1"}""",
        """{"hash":"0xc7fbaf8131e8143f977dbae0600d8babb6eb442a805f95d8173d018e2ad4a435","height":4,"local_timestamp":1745893800229530000,"parent_hash":"0x3cd9457e80f224e5db5d5b9f6ca95cb94261bc42110b4c638f43624e2b287500","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893800445355226,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x21bb2083f22f6adb1cc9988851de819fca40775904bb7d2a44d5f92562eb5ad4","signatures":["0xa059e737e32658a18c3f8dccd9698513a446d57281e7a097daa88f8b6237bb6d254231096bd98e6f27e608b8cef32ed9e12080fef62591d6ead49137b6a0348c"],"slot_id":1,"state_root":"0x2337c4e596304c786aad1686b16d12397d67bbbdb8829f4050f926355d7b85af","transactions":[{"block_hash":"0xc7fbaf8131e8143f977dbae0600d8babb6eb442a805f95d8173d018e2ad4a435","dependent_transaction_hash":"","fuel":1000000,"hash":"0x94a06e73a2ddb1b1dc907c3f2d7b7345d47760612f5be452bbe6a6b6bb3b696a","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xf058f7e44c4bb56e76c9705adc83037b832c0168c6ea1cbfaef968cda40ab343dc892055ea2adb5b264f2623be4b6f7891903b3f334438ad8e99b82238350883"],"status":true,"timestamp":1745893800229530000,"transaction_index":0}],"transactions_root":"0xbf0283cb7fe6186619d789fd0ef9f8ed89b8eec743cd542c5ee5787967a722da"}""",
        """{"hash":"0xeb49e00316919be0b139aacba13a75652ce6015ad2f2a1160139e00bd80634eb","height":5,"local_timestamp":1745893815266746000,"parent_hash":"0xc7fbaf8131e8143f977dbae0600d8babb6eb442a805f95d8173d018e2ad4a435","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893815482571226,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xab89e093bc58ff5f7743cb0081a9f47d6f7353499f4934e7caf2243e1b00ebf4","signatures":["0x92e49c55b974fa2d2aa4ad3a9a4754db90bcb4c22e28f085e04b984f4fd82538e820f8639cd36df72c18747a321d5163dc6cda6338cea9f17e2b54d2ff75af80"],"slot_id":1,"state_root":"0x9eeaeb9b149ec0f34073396396de11ec8e94994d0e36b53937a5908aae2fa70f","transactions":[{"block_hash":"0xeb49e00316919be0b139aacba13a75652ce6015ad2f2a1160139e00bd80634eb","dependent_transaction_hash":"","fuel":1000000,"hash":"0x77fc73cf89237a727e23bb36e8ed343100ee6719835a6ebc5b188708de39c308","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x78847117ff61441f16a47d320920fbb97fc41dc5d64737c09d2185829be43c072f3ef3ac7af7e8bf03965c9c2d59b3db6e7944dabefc7b0476c9ec67fb452a83"],"status":true,"timestamp":1745893815266746000,"transaction_index":0}],"transactions_root":"0xa059b8420fc09f83dcfa6f7f6dee9f4b52ad3902b9648bc9ccfdfef25cffa816"}""",
        """{"hash":"0xb0447ec6298e626522a0065872a2e97f2fffe4d2de30cd7a326a03cf4a255bad","height":6,"local_timestamp":1745893830307228000,"parent_hash":"0xeb49e00316919be0b139aacba13a75652ce6015ad2f2a1160139e00bd80634eb","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893830523054226,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x569e1be668ab53cf3afadf64b18aac7e65db1c475813b1d49cf2bf48b1871a2f","signatures":["0x9c50c813a3ca0655f4ccf6ca08f33d6b286f40c600b34a042fb09c056b0cde51274712083c609c4cdd4e6321ea0cbbc08d56253f363cf685d2af747d3849cd84"],"slot_id":1,"state_root":"0x288a516a93d935e3a7654324763bf49c36172d0c39b2708112b1127b6a676ca3","transactions":[{"block_hash":"0xb0447ec6298e626522a0065872a2e97f2fffe4d2de30cd7a326a03cf4a255bad","dependent_transaction_hash":"","fuel":1000000,"hash":"0xf035509d0121ceac17028b04d933cbbae7340752a86d5584ddbbc87d4705cdcb","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xde6402c1bee2e0634dcd9390551b18f481df19e8ce9fb094e5870b463d8c2a1befb4b891e08ec940a68a2cdec7eb062736b4daba38e1b021b90f47d7af51ad8c"],"status":true,"timestamp":1745893830307228000,"transaction_index":0}],"transactions_root":"0x48fc04c242a8c6b7fc999345cfa43faa748b1800ea36891cb4b2c0b7459b4e9f"}""",
        """{"hash":"0xa62b7cd4f4bda195fd6d77029254ca4f7ac14f6b5b71fe211dfa96983278b67e","height":7,"local_timestamp":1745893845347132000,"parent_hash":"0xb0447ec6298e626522a0065872a2e97f2fffe4d2de30cd7a326a03cf4a255bad","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745893845537566647,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x23fc5adf0de1bbd4c92c3bf8186eb19c428b3aa447213eea114292927dea4c9e","signatures":["0x58e531a223112fc0923c497d16d38a494e4690562e9553e3dfc70ac46c00563280694aca0cfbfaa0a4dba9c27fabb543dacbbbe0cbbebfdabb35aac06a020d81"],"slot_id":1,"state_root":"0xd1670b0822f844441f76370358636eecec4b29d22f7baec980ea7b7c42067a19","transactions":[{"block_hash":"0xa62b7cd4f4bda195fd6d77029254ca4f7ac14f6b5b71fe211dfa96983278b67e","dependent_transaction_hash":"","fuel":1000000,"hash":"0x05d17b967fedb66ab2604470cf67472ed317324107faee7364a05db8d197641a","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xfc986e3f5912453f08d76530e55d65cd39528f955ac36008073379d4a941f660073faf47c571645a8c0ada202938f69cc7e43286396cd84434567d01a57c0680"],"status":true,"timestamp":1745893845347132000,"transaction_index":0}],"transactions_root":"0x9d93978f8b01374b5fe24f01bda86eddccc1b89dee2c85e7e4f9447ca9a51b34"}""",
        """{"hash":"0x7e9736d10a8ddb0fe07e8b269ba55cd2662bf874a0030541802ca7934b7cfd9a","height":8,"local_timestamp":1745894625193728000,"parent_hash":"0xa62b7cd4f4bda195fd6d77029254ca4f7ac14f6b5b71fe211dfa96983278b67e","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745894625429739419,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xad904937767de98b915b1b3ebd06f61270d894b5496fe179dc95d221ec6a8925","signatures":["0xc8310cb2d3330ed0be547675aca40a0bc0e7850a1d4a2cbbe492bdbeb6b18f3fbb88abbc5d8f1429cf2600466090987b7dc897944ba3d50bb2536f4a0a767d8e"],"slot_id":1,"state_root":"0xff8df398649f9e0d50611fa2630083fb68c53d097465efa5c4c44c7f504c3c32","transactions":[{"block_hash":"0x7e9736d10a8ddb0fe07e8b269ba55cd2662bf874a0030541802ca7934b7cfd9a","dependent_transaction_hash":"","fuel":1000000,"hash":"0x68830a65e2f850b987713bc75ea408a1510374dc0ecbf5b529bc352cf655eccc","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x36c2e018076f26d5175e8284eb5e92d387f853ac71d3a9def78a751e9449173af6db747419907ae6b109e32038b7ccda3c6f2a513900501d4c416f48d4ff7a81"],"status":true,"timestamp":1745894625193728000,"transaction_index":0}],"transactions_root":"0x92d617452403a70f4e1b4920a48a18d530798d9740d9811e2874b9acd6d5b89a"}""",
        """{"hash":"0x3c2527a1eb5eb20089082200d1da9985aef02ad9a4f1cc01b49ff91549a8d16a","height":9,"local_timestamp":1745894640228368000,"parent_hash":"0x7e9736d10a8ddb0fe07e8b269ba55cd2662bf874a0030541802ca7934b7cfd9a","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745894640464385419,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xe4495c4f100d5fa38cbd6957970dabfbf2d5a51253a0f359fe5895be5d1cff70","signatures":["0x3c245d5c112658df989d5ece3fc3f276802229946b31c2cf70635ce7032c33490b643f8d7260dd05404db002f5d68c23a8fd660fb12b761ab3db6e275d52128e"],"slot_id":1,"state_root":"0x0a1a6de96834e5b245a69dc61a9359718debac846bdd71c7fa55940dcd22631f","transactions":[{"block_hash":"0x3c2527a1eb5eb20089082200d1da9985aef02ad9a4f1cc01b49ff91549a8d16a","dependent_transaction_hash":"","fuel":1000000,"hash":"0xe9fff8077a1020b2f2c7751625c46b780c769ff949e6406afdca67f47b411bb9","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x28a52e0edff6cb568f2607ab3fc78ddf7b5fe35ec6478984bc53061a8c47375a3eef1efbf9ef86585da257cda886336cf2fca5a7d1b419f111ac5720b1c48188"],"status":true,"timestamp":1745894640228368000,"transaction_index":0}],"transactions_root":"0xb6979433c618fc62d7a93e44aefefea7e37281d3c19aea578ff8de5c4dbd2ca0"}""",
        """{"hash":"0x67fdc2fd3ccd5c52d12d72bbcd4f7406f1dfde76d5fd579337ebb23d1fb40c85","height":10,"local_timestamp":1745894655261018000,"parent_hash":"0x3c2527a1eb5eb20089082200d1da9985aef02ad9a4f1cc01b49ff91549a8d16a","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745894655466429341,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x12f1ba9d453f3932612a896c709e073a00d569e295f1df42dc2328849c4da926","signatures":["0x60425d6879bb96915fda244a68b8500260faea9572c8dba62146d60958729f14b418b38ce7d7df03546c7eb35ebdb8943f10e1313a534f75030c8b2ed843c88d"],"slot_id":1,"state_root":"0x180b95351e380ac4357fce418c3641e1d7d03aa8f20bd08ce8b3476e22dda871","transactions":[{"block_hash":"0x67fdc2fd3ccd5c52d12d72bbcd4f7406f1dfde76d5fd579337ebb23d1fb40c85","dependent_transaction_hash":"","fuel":1000000,"hash":"0x2c2c27930c51e1323c34e0b7d916ac94c20fdaf4fe07c281bb1891e68e697ae9","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xd40b41c707033d6d5d4606adc8efbc52df75c714cc6ef5f7e8d4a41a415a6e6b8d5169e5f74cbd3859e13ac478ab131ff2152e8cc0e532322bb469dc42002c86"],"status":true,"timestamp":1745894655261018000,"transaction_index":0}],"transactions_root":"0x8d25395f7c5fe2ed3e9c1be87ffb99f5da9fa2fca3a6a69191cfd52a56d2ea9b"}""",
        """{"hash":"0xfb008f1e366ae5ddf80f11b243d27c73aad40f3d43aabfd3dea016e527fa5aa2","height":11,"local_timestamp":1745894670303113000,"parent_hash":"0x67fdc2fd3ccd5c52d12d72bbcd4f7406f1dfde76d5fd579337ebb23d1fb40c85","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745894670508524341,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xaf27cf012dd2a8210df38ab6db04a368846b879c95e47164876a7ec0ce4ca663","signatures":["0x422b92fdf1e8138e06dba71cb26b1988f698e2af7815d10fdffcadba6b9fd73acf64dfd0516e382012643020d9047ae228efa5c538b7ab342191f37e367dad8e"],"slot_id":1,"state_root":"0x86b99063d157d4946569abcc69a8f91e5f24dcbe28a4ae2cb23478eb6189d19b","transactions":[{"block_hash":"0xfb008f1e366ae5ddf80f11b243d27c73aad40f3d43aabfd3dea016e527fa5aa2","dependent_transaction_hash":"","fuel":1000000,"hash":"0x8a15d9f03f8c23423d459de27b9598b4f236c7d521fa003cd5347179d5299c0a","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xce216e27192651f222ff5e679730e8b9bf761bac1047f6ac1b2315d0914eaf082eb7caa2fbed105bdc4c2667eff816530aebc6cd5317896732d3a84b9968e88f"],"status":true,"timestamp":1745894670303113000,"transaction_index":0}],"transactions_root":"0x5b1135338518e64515e5d023f39981dcd6d20431c7a7ee49ca7d797b61b0bb28"}""",
        """{"hash":"0x4e8a2ce9c5fa8b6a3cabdab8293bf80b56c975fbed23b2a45e3261287af2dffd","height":12,"local_timestamp":1745894685333420000,"parent_hash":"0xfb008f1e366ae5ddf80f11b243d27c73aad40f3d43aabfd3dea016e527fa5aa2","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745894685538832341,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xa74cac43191c31aeb578344c56355990ea59eec2ff359d9b04b8851b41435b91","signatures":["0xb4548c9418541c0a8f6709058b666ae6a9b420738f2583f0e71b23390cf70c615a087fd7694798d0b7393968a9d269869ab9fe711d9b430578e18c25f6771e82"],"slot_id":1,"state_root":"0x6669b3aca4057372da2a607cd54351182f9d3edd744103db63ecf7fc1c6f885c","transactions":[{"block_hash":"0x4e8a2ce9c5fa8b6a3cabdab8293bf80b56c975fbed23b2a45e3261287af2dffd","dependent_transaction_hash":"","fuel":1000000,"hash":"0x3a71b6676bb0abcbe954e1ccd3b9cf4a6f15a213b0ab2545f02b50614924454a","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xfafe746479c90fcfbeb892b86cdd411393f980d14cdc2949fbca8e01c4b0c233dc87cc8536247a0838da15e8609ae962b059b53a1fc635b0749f97df7c4c6a83"],"status":true,"timestamp":1745894685333420000,"transaction_index":0}],"transactions_root":"0x1dd8298a5af7c000eb98933d00b6623f79b5e2c83e55b662e95b58db18c9445e"}""",
        """{"hash":"0x185abc30a9c37c2b6e2be640063ba4cbf4861d0777c9848dfd9fe7b11d0433df","height":13,"local_timestamp":1745894700362170000,"parent_hash":"0x4e8a2ce9c5fa8b6a3cabdab8293bf80b56c975fbed23b2a45e3261287af2dffd","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1745894700567581341,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x1936f3c5c19825b1bf01d58a2af41aa57d986fc5c2f5fd28c5cb292b0a1eae6e","signatures":["0x984b9d3fb9b30eca11db6a01b85deb1a93916a32041b0db027f54e4147ae2f0721abd7bce9efd7ff4d91cfb9ca61fabb18ccaf34504afb3e80d897a4ad4f9489"],"slot_id":1,"state_root":"0x9b46d8d43e254d9da87a41f3439e074e75cdd6b3aef0338c28327d9c801aa170","transactions":[{"block_hash":"0x185abc30a9c37c2b6e2be640063ba4cbf4861d0777c9848dfd9fe7b11d0433df","dependent_transaction_hash":"","fuel":1000000,"hash":"0x68a87b26cf2b6d3815899cf762ced44ca1b3cdcf8440785d201ea0366bec527c","op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xa4462c066f0956572a9bf92dbc7733553af405758766b46f61b56bc5acad186b91a5169b91a8e7101cbcba78c35c243e2bc03b98beab919c68270a679746d886"],"status":true,"timestamp":1745894700362170000,"transaction_index":0}],"transactions_root":"0x2f4a6413f98e65933bdaed5c055f0b6e787fbbe16f5508ca25c56d6ae7700ea0"}""",
    ]
    for blockJson in blocksJson:
        _result, err = explorerClient.execute("insert_block", None, blockJson)
        assert err is None

    max_int64 = 2**63 - 1

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions",
        str,
        False,
        "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        0,
        max_int64,
        100,
    )
    assert err is None
    print(result)

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions", str, True, "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06", max_int64, 0, 100
    )
    assert err is None
    print(result)

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions",
        str,
        False,
        "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        1739773035410233000,
        1739773140669267000,
        100,
    )
    assert err is None
    print(result)

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions",
        str,
        True,
        "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        1739773140669267000,
        1739773035410233000,
        100,
    )
    assert err is None
    print(result)

    # Test get_block_by_hash
    # Use the first block from processed_blocks instead of TEST_BLOCKS_DATA
    first_block = json.loads(blocksJson[0])
    result, err = explorerClient.executeReadOnly("get_block_by_hash", str, first_block["hash"])
    assert err is None
    assert json.loads(result)["hash"] == first_block["hash"]

    # Test get_block_by_hash with non-existent hash
    result, err = explorerClient.executeReadOnly(
        "get_block_by_hash", str, "0x0000000000000000000000000000000000000000000000000000000000000000"
    )
    assert err is not None

    # Test get_transaction_by_hash
    first_transaction = first_block["transactions"][0]
    result, err = explorerClient.executeReadOnly("get_transaction_by_hash", str, first_transaction["hash"])
    assert err is None
    assert json.loads(result)["hash"] == first_transaction["hash"]

    # Test get_transaction_by_hash with non-existent hash
    result, err = explorerClient.executeReadOnly(
        "get_transaction_by_hash", str, "0x0000000000000000000000000000000000000000000000000000000000000000"
    )
    assert err is not None

    # Test get_synced_block_height
    result, err = explorerClient.executeReadOnly("get_synced_block_height", int)
    assert err is None
    # The last block has height 13
    assert result == 13

    print("test end")


def test_explorer_db_transaction_with_receipt_data():
    # Create a new explorer client
    explorerClient = ContractTester(
        wasmName="explorer_db",
    )
    explorerClient.constructor()

    # Create a simple block with transaction that includes receipt fields
    simple_block = json.dumps(
        {
            "hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "height": 0,
            "local_timestamp": 0,
            "parent_hash": "0x00000000000000000000000000000000",
            "proposer_address": "0x00000000000000000000000000000000",
            "protocol_timestamp": 0,
            "public_keys": [],
            "receipts_root": "0x00000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x00000000000000000000000000000000",
            "transactions": [
                {
                    "dependent_transaction_hash": "",
                    "fuel": 1000,
                    "hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "op_data": {
                        "op_type": 1,
                        "contract_address": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "function_name": "transfer",
                        "parameters": [
                            "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                            "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a01",
                            100,
                        ],
                    },
                    "public_keys": [],
                    "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                    "signatures": [],
                    "timestamp": 0,
                    "transaction_index": 1,
                    "status": True,
                    "op_result": {"op_type": 1, "return_data": None},
                    "block_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                }
            ],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )

    # Insert block with transaction that includes receipt data
    _result, err = explorerClient.execute("insert_block", None, simple_block)
    assert err is None

    # Test get_transaction_by_hash to verify receipt data is included
    result, err = explorerClient.executeReadOnly(
        "get_transaction_by_hash", str, "0x1234567890123456789012345678901234567890123456789012345678901234"
    )
    assert err is None
    transaction = json.loads(result)
    assert transaction["hash"] == "0x1234567890123456789012345678901234567890123456789012345678901234"
    assert transaction["transaction_index"] == 1
    assert transaction["status"] is True
    assert "op_result" in transaction
    assert "block_hash" in transaction
    assert "op_data" in transaction
    assert transaction["op_data"]["op_type"] == 1
    assert transaction["op_data"]["function_name"] == "transfer"

    # Test get_transaction_op_result
    result, err = explorerClient.executeReadOnly(
        "get_transaction_op_result", str, "0x1234567890123456789012345678901234567890123456789012345678901234"
    )
    assert err is None
    op_result = json.loads(result)
    assert op_result["op_type"] == 1  # CallContract
    assert "return_data" in op_result

    # Test get_transaction_op_data
    result, err = explorerClient.executeReadOnly(
        "get_transaction_op_data", str, "0x1234567890123456789012345678901234567890123456789012345678901234"
    )
    assert err is None
    op_data = json.loads(result)
    assert op_data["op_type"] == 1  # CallContract
    assert op_data["function_name"] == "transfer"


def test_explorer_db_block_errors():
    """Test error handling when inserting invalid blocks"""
    # Create a new explorer client
    explorerClient = ContractTester(
        wasmName="explorer_db",
    )
    explorerClient.constructor()

    # First insert a valid block as a base
    valid_block = json.dumps(
        {
            "hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "height": 0,
            "local_timestamp": 0,
            "parent_hash": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 0,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, valid_block)
    assert err is None

    # Test 1: Insert a block with non-continuous height (skipping height 1)
    non_continuous_block = json.dumps(
        {
            "hash": "0x2234567890123456789012345678901234567890123456789012345678901234",
            "height": 2,  # Skip height 1
            "local_timestamp": 1,
            "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 1,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, non_continuous_block)
    assert err is not None
    assert "Block height is not continuous" in str(err)

    # Test 2: Insert a block with mismatched parent_hash
    wrong_parent_block = json.dumps(
        {
            "hash": "0x3234567890123456789012345678901234567890123456789012345678901234",
            "height": 1,
            "local_timestamp": 1,
            "parent_hash": "0x9999999999999999999999999999999999999999999999999999999999999999",  # Incorrect parent hash
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 1,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, wrong_parent_block)
    assert err is not None
    assert "Block parent hash does not match" in str(err)

    # Test 3: Insert a duplicate block (block with the same hash)
    # First insert a valid block at height 1
    valid_block_1 = json.dumps(
        {
            "hash": "0x4234567890123456789012345678901234567890123456789012345678901234",
            "height": 1,
            "local_timestamp": 1,
            "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 1,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, valid_block_1)
    assert err is None

    # Try to insert another block with the same hash but different height
    duplicate_block = json.dumps(
        {
            "hash": "0x4234567890123456789012345678901234567890123456789012345678901234",  # Same hash as the block above
            "height": 2,
            "local_timestamp": 2,
            "parent_hash": "0x4234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 2,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    # Since the block already exists, it should cause an error
    _result, err = explorerClient.execute("insert_block", None, duplicate_block)
    assert err is not None

    # Test 4: Insert a block with invalid format (missing required fields)
    # Create a block missing the height field
    invalid_format_block = json.dumps(
        {
            "hash": "0x5234567890123456789012345678901234567890123456789012345678901234",
            # Missing height field
            "local_timestamp": 2,
            "parent_hash": "0x4234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 2,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, invalid_format_block)
    assert err is not None
    # Due to JSON parsing error, it should return an error
    assert "missing field" in str(err).lower() or "error" in str(err).lower()
