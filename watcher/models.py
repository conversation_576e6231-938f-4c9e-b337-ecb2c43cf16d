from __future__ import annotations

from dataclasses import dataclass
from typing import List

from serde import field, serde

from common import bytesToHex
from common.models import (
    Block,
    OperationCallContractResult,
    OperationResult,
    OperationType,
    Receipt,
    Transaction,
    TransactionExecutionStatus,
    TransactionOperationData,
)
from watcher.logger import Logger


@serde
@dataclass
class StoreTransaction:
    hash: str
    sender: str
    timestamp: int  # timestamp of when the transaction is received

    dependent_transaction_hash: str

    # operation
    op_data: TransactionOperationData = field(deserializer=TransactionOperationData.deserializeFromDict)

    # fuel
    fuel: int = 0

    # signature
    public_keys: List[str] = field(default_factory=list)
    signatures: List[str] = field(default_factory=list)

    # receipt fields
    transaction_index: int = 0
    status: bool = False
    op_result: OperationResult = field(
        deserializer=OperationResult.deserializeFromDict,
        default_factory=OperationCallContractResult(op_type=OperationType.CALL_CONTRACT, return_data=None),
    )
    block_hash: str = ""

    @staticmethod
    def fromTransactionAndReceipt(transaction: Transaction, receipt: Receipt) -> StoreTransaction:
        return StoreTransaction(
            hash=bytesToHex(transaction.hash()),
            sender=transaction.sender,
            timestamp=transaction.timestamp,
            dependent_transaction_hash=transaction.dependent_transaction_hash,
            op_data=transaction.op_data,
            fuel=transaction.fuel,
            public_keys=transaction.public_keys,
            signatures=transaction.signatures,
            transaction_index=receipt.transaction_index,
            status=receipt.status == TransactionExecutionStatus.SUCCESS,
            op_result=receipt.op_result,
            block_hash=bytesToHex(receipt.block_hash),
        )


@serde
@dataclass
class StoreBlock:
    hash: str

    parent_hash: str
    height: int
    state_root: str
    transactions_root: str
    receipts_root: str

    local_timestamp: int
    protocol_timestamp: int
    slot_id: int
    proposer_address: str

    # public keys of validators
    public_keys: List[str] = field(default_factory=list)

    # signatures of validators
    signatures: List[str] = field(default_factory=list)

    transactions: List[StoreTransaction] = field(default_factory=list)

    @staticmethod
    def fromBlock(block: Block, receipts: List[Receipt] = None) -> StoreBlock:
        # Create a dictionary to map transaction hash to receipt
        receipt_map = {}
        if receipts:
            for receipt in receipts:
                receipt_map[bytesToHex(receipt.transaction_hash)] = receipt

        # Create transactions with receipts if available
        transactions = []
        for tx in block.transactions:
            tx_hash = bytesToHex(tx.hash())
            if tx_hash in receipt_map:
                transactions.append(StoreTransaction.fromTransactionAndReceipt(tx, receipt_map[tx_hash]))
            else:
                Logger.error(f"Transaction hash {tx_hash} not found in block {block.hash}", "RECEIPT")

        return StoreBlock(
            hash=block.hashHex(),
            parent_hash=bytesToHex(block.header.parent_hash),
            height=block.header.height,
            state_root=block.header.state_root,
            transactions_root=block.header.transactions_root,
            receipts_root=block.header.receipts_root,
            local_timestamp=block.header.local_timestamp,
            protocol_timestamp=block.header.protocol_timestamp,
            slot_id=block.header.slot_id,
            proposer_address=bytesToHex(block.header.proposer_address),
            public_keys=block.header.public_keys,
            signatures=block.header.signatures,
            transactions=transactions,
        )
