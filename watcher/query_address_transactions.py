from __future__ import annotations

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta, timezone
from typing import Any, List

# Add the project root to Python path to ensure imports work
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from common import fromJson

# Import shared classes from watcher modules
from watcher.config import WatcherConfig
from watcher.connection import ConnectionManager
from watcher.logger import Logger
from watcher.models import StoreTransaction


async def get_address_transactions(
    user_address: str,
    contract_address: str,
    conn_manager: ConnectionManager,
    config: WatcherConfig,
    start_time: int = 0,
    end_time: int = 9999999999999999999,
    include_reverted: bool = False,
    limit: int = 100,
) -> List[StoreTransaction]:
    """Query transactions from a specific user address"""
    Logger.info(f"Querying transactions for address: {user_address}", "TX")

    session = await conn_manager.get_sidenet_session()

    params = {
        "contract_address": contract_address,
        "function_name": "get_address_transactions",
        "args": [include_reverted, user_address, start_time, end_time, limit],
        "signatures": [],
        "publickeys": [],
        "privatekey": config.private_key,
    }

    try:
        result = await session.send_request("contract.query", [params])
        transactions = fromJson(List[StoreTransaction], result["result"])
        Logger.info(f"Found {len(transactions)} transactions for address {user_address}", "TX")
        return transactions
    except Exception as e:
        Logger.error(f"Failed to query transactions: {e}", "TX")
        return []


def format_parameters(params: List[Any]) -> str:
    """
    Format parameter list to make it more readable
    """
    if not params:
        return "[]"

    try:
        # Try to convert parameters to a more readable format
        formatted = json.dumps(params, indent=2, ensure_ascii=False)
        # Truncate if too long
        if len(formatted) > 200:
            return formatted[:200] + "..."
        return formatted
    except Exception:
        # Return original string if formatting fails
        return str(params)


def parse_operation_result(op_result) -> dict:
    """
    Parse operation result object into a structured dictionary
    """
    try:
        # If op_result is already an OperationResult object, convert it to dict
        from common import fromJson, toJson

        result_dict = fromJson(dict, toJson(op_result))
        return result_dict
    except Exception:
        # If conversion fails, try to handle it as a string
        try:
            if isinstance(op_result, str):
                return json.loads(op_result)
            else:
                return {"error": "Unknown format", "raw": str(op_result)}
        except json.JSONDecodeError:
            return {"error": "Invalid JSON format", "raw": str(op_result)}
        except Exception as e:
            return {"error": str(e), "raw": str(op_result)}


async def main():
    # Load configuration and initialize manager
    config = WatcherConfig()
    conn_manager = ConnectionManager(config)

    try:
        # Check if contract address exists
        contract_address = config.contract_address
        if not contract_address:
            Logger.error("No contract address found in config. Please run vgraph_watcher.py first.", "CONTRACT")
            return

        Logger.info(f"Using contract address: {contract_address}", "CONTRACT")

        # Example user address - replace with actual address or command line argument
        user_address = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"

        # Query transactions
        transactions = await get_address_transactions(
            user_address=user_address,
            contract_address=contract_address,
            conn_manager=conn_manager,
            config=config,
            include_reverted=True,
            start_time=9999999999999999999,
            end_time=0,
        )

        # Process the transactions with pretty printing
        if transactions:
            Logger.info(f"--- Found {len(transactions)} Transactions for Address {user_address} ---", "TX")
            for i, tx in enumerate(transactions):
                print(f"Transaction #{i + 1}:")
                print(f"  Hash: {tx.hash}")
                print(f"  Sender: {tx.sender}")
                print(
                    f"  Timestamp: {tx.timestamp} (CN Time: {datetime.fromtimestamp(tx.timestamp / 1e9, tz=timezone(timedelta(hours=8))).strftime('%Y-%m-%d %H:%M:%S.%f')})"
                )
                print(f"  Dependent Hash: {tx.dependent_transaction_hash}")

                # Display op_data information
                print("  Operation Data:")
                print(f"    Type: {tx.op_data.op_type.name}")

                # Display different information based on operation type
                if tx.op_data.op_type.name == "CALL_CONTRACT":
                    print(f"    Contract Address: {tx.op_data.contract_address}")
                    print(f"    Function Name: {tx.op_data.function_name}")
                    print(f"    Parameters: {format_parameters(tx.op_data.parameters)}")
                elif tx.op_data.op_type.name == "CREATE_CONTRACT":
                    print(f"    Contract Bytecode: {tx.op_data.contract_hex_bytecode[:10]}...")
                    print(f"    Constructor Parameters: {format_parameters(tx.op_data.constructor_parameters)}")
                    print(f"    Source URL: {tx.op_data.contract_source_url}")
                    print(f"    Upgradable: {tx.op_data.upgradable}")
                    print(f"    Git Commit: {tx.op_data.git_commit_hash}")
                    print(f"    Reproducible: {tx.op_data.reproducible_build}")
                elif tx.op_data.op_type.name == "FORK_CONTRACT":
                    print(f"    Contract Code Hash: {tx.op_data.contract_code_hash}")
                    print(f"    Constructor Parameters: {format_parameters(tx.op_data.constructor_parameters)}")
                    print(f"    Source URL: {tx.op_data.contract_source_url}")
                    print(f"    Upgradable: {tx.op_data.upgradable}")
                    print(f"    Git Commit: {tx.op_data.git_commit_hash}")
                    print(f"    Reproducible: {tx.op_data.reproducible_build}")
                elif tx.op_data.op_type.name == "UPGRADE_CONTRACT":
                    print(f"    Contract Address: {tx.op_data.contract_address}")
                    print(f"    Contract Bytecode: {tx.op_data.contract_hex_bytecode[:10]}...")
                    print(f"    Source URL: {tx.op_data.contract_source_url}")
                    print(f"    Git Commit: {tx.op_data.git_commit_hash}")
                    print(f"    Reproducible: {tx.op_data.reproducible_build}")

                print(f"  Fuel: {tx.fuel}")
                print(f"  Public Keys: {tx.public_keys}")
                print(f"  Signatures: {tx.signatures}")

                # Receipt related information
                print("  Receipt Information:")
                print(f"    Transaction Index: {tx.transaction_index}")
                print(f"    Status: {'Success' if tx.status else 'Failed'}")
                if tx.op_result:
                    # Parse operation result
                    op_result = parse_operation_result(tx.op_result)

                    if "error" in op_result:
                        print(f"    Operation Result: {op_result.get('raw')} ({op_result.get('error')})")
                    else:
                        print("    Operation Result:")
                        # Get operation type - could be an enum value or a string
                        op_type = op_result.get("op_type")
                        if isinstance(op_type, int) or op_type.isdigit():
                            op_type_int = int(op_type)
                            op_type_name = {
                                0: "CREATE_CONTRACT",
                                1: "CALL_CONTRACT",
                                2: "FORK_CONTRACT",
                                3: "UPGRADE_CONTRACT",
                            }.get(op_type_int, f"UNKNOWN({op_type_int})")
                        else:
                            op_type_name = op_type
                            op_type_int = {
                                "CREATE_CONTRACT": 0,
                                "CALL_CONTRACT": 1,
                                "FORK_CONTRACT": 2,
                                "UPGRADE_CONTRACT": 3,
                            }.get(op_type, -1)

                        print(f"      Type: {op_type_name}")

                        # Display different information based on operation type
                        if op_type_int == 0:  # CREATE_CONTRACT
                            print(f"      Contract Address: {op_result.get('contract_address')}")
                            print(f"      Code Hash: {op_result.get('code_hash')}")
                        elif op_type_int == 1:  # CALL_CONTRACT
                            print(f"      Return Data: {op_result.get('return_data')}")
                        elif op_type_int == 2:  # FORK_CONTRACT
                            print(f"      Contract Address: {op_result.get('contract_address')}")
                            print(f"      Code Hash: {op_result.get('code_hash')}")
                        elif op_type_int == 3:  # UPGRADE_CONTRACT
                            print(f"      Code Hash: {op_result.get('code_hash')}")
                if tx.block_hash:
                    print(f"    Block Hash: {tx.block_hash}")
                print("-" * 50)  # Longer separator line
        else:
            Logger.info(f"No transactions found for address {user_address}", "TX")

    finally:
        # Ensure connections are closed
        await conn_manager.close()


if __name__ == "__main__":
    try:
        asyncio.get_event_loop().run_until_complete(main())
    except KeyboardInterrupt:
        Logger.info("Query stopped by user")
    except Exception as e:
        Logger.error(f"Fatal error: {e}")
